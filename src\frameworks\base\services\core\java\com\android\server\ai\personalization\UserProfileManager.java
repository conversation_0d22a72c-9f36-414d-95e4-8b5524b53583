/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.personalization;

import android.ai.UserInteraction;
import android.ai.UserProfile;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.util.Slog;

import com.android.server.ai.security.AiSecurityManager;

import java.io.PrintWriter;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages user profiles for AI personalization in Jarvis OS.
 * 
 * Maintains user preferences, communication styles, learned patterns,
 * and behavioral adaptations while ensuring privacy protection.
 */
public class UserProfileManager {
    private static final String TAG = "UserProfileManager";
    private static final boolean DEBUG = true;

    // Profile categories
    private static final String CATEGORY_COMMUNICATION = "communication";
    private static final String CATEGORY_BEHAVIOR = "behavior";
    private static final String CATEGORY_PREFERENCES = "preferences";
    private static final String CATEGORY_PATTERNS = "patterns";
    private static final String CATEGORY_PRIVACY = "privacy";

    private final Context mContext;
    private final PreferenceStorage mPreferenceStorage;
    private final AiSecurityManager mSecurityManager;
    
    // User profiles cache
    private final ConcurrentHashMap<String, UserProfile> mUserProfiles = new ConcurrentHashMap<>();
    
    // Statistics
    private long mProfileUpdates = 0;
    private long mInteractionLearning = 0;

    public UserProfileManager(Context context, PreferenceStorage preferenceStorage, 
                             AiSecurityManager securityManager) {
        mContext = context;
        mPreferenceStorage = preferenceStorage;
        mSecurityManager = securityManager;
        
        if (DEBUG) Slog.d(TAG, "UserProfileManager initialized");
    }

    /**
     * Initialize system service connections
     */
    public void initializeSystemServiceConnections() {
        // Initialize connections to other system services if needed
        if (DEBUG) Slog.d(TAG, "System service connections initialized");
    }

    /**
     * Initialize default user profiles
     */
    public void initializeDefaultProfiles() {
        // Create default system profile
        UserProfile systemProfile = createDefaultProfile("system");
        mUserProfiles.put("system", systemProfile);
        
        if (DEBUG) Slog.d(TAG, "Default profiles initialized");
    }

    /**
     * Get user profile for a package
     */
    public UserProfile getUserProfile(String packageName) {
        UserProfile profile = mUserProfiles.get(packageName);
        
        if (profile == null) {
            // Create new profile for the package
            profile = createDefaultProfile(packageName);
            
            // Load existing preferences
            Bundle existingPreferences = mPreferenceStorage.getPreferences(packageName);
            if (existingPreferences != null) {
                profile.preferences.putAll(existingPreferences);
            }
            
            mUserProfiles.put(packageName, profile);
            
            if (DEBUG) Slog.d(TAG, "Created new user profile for: " + packageName);
        }
        
        return profile;
    }

    /**
     * Update user profile from interaction
     */
    public void updateProfileFromInteraction(UserInteraction interaction, String packageName) {
        UserProfile profile = getUserProfile(packageName);
        
        try {
            // Update communication style
            updateCommunicationStyle(profile, interaction);
            
            // Update behavioral patterns
            updateBehavioralPatterns(profile, interaction);
            
            // Update preferences based on interaction
            updatePreferencesFromInteraction(profile, interaction);
            
            // Update profile timestamp
            profile.lastUpdated = System.currentTimeMillis();
            
            // Save updated profile
            saveUserProfile(packageName, profile);
            
            mInteractionLearning++;
            
            if (DEBUG) Slog.d(TAG, "Profile updated from interaction: " + interaction.interactionType);
            
        } catch (Exception e) {
            Slog.e(TAG, "Error updating profile from interaction", e);
        }
    }

    /**
     * Import user profile from external data
     */
    public void importUserProfile(String packageName, Bundle profileData) {
        try {
            UserProfile profile = getUserProfile(packageName);
            
            // Import preferences
            Bundle preferences = profileData.getBundle("preferences");
            if (preferences != null) {
                profile.preferences.putAll(preferences);
            }
            
            // Import communication style
            Bundle communicationStyle = profileData.getBundle("communication_style");
            if (communicationStyle != null) {
                profile.communicationStyle.putAll(communicationStyle);
            }
            
            // Import learned patterns
            Bundle learnedPatterns = profileData.getBundle("learned_patterns");
            if (learnedPatterns != null) {
                profile.learnedPatterns.putAll(learnedPatterns);
            }
            
            // Import privacy settings
            Bundle privacySettings = profileData.getBundle("privacy_settings");
            if (privacySettings != null) {
                profile.privacySettings.putAll(privacySettings);
            }
            
            profile.lastUpdated = System.currentTimeMillis();
            saveUserProfile(packageName, profile);
            
            if (DEBUG) Slog.d(TAG, "User profile imported for: " + packageName);
            
        } catch (Exception e) {
            Slog.e(TAG, "Error importing user profile", e);
        }
    }

    /**
     * Reset user profile for a package
     */
    public void resetUserProfile(String packageName) {
        UserProfile defaultProfile = createDefaultProfile(packageName);
        mUserProfiles.put(packageName, defaultProfile);
        saveUserProfile(packageName, defaultProfile);
        
        if (DEBUG) Slog.d(TAG, "User profile reset for: " + packageName);
    }

    /**
     * Get profile statistics
     */
    public Bundle getProfileStatistics() {
        Bundle stats = new Bundle();
        stats.putInt("total_profiles", mUserProfiles.size());
        stats.putLong("profile_updates", mProfileUpdates);
        stats.putLong("interaction_learning", mInteractionLearning);
        
        return stats;
    }

    /**
     * Dump profile manager state
     */
    public void dump(PrintWriter pw) {
        pw.println("    UserProfileManager State:");
        pw.println("      Total Profiles: " + mUserProfiles.size());
        pw.println("      Profile Updates: " + mProfileUpdates);
        pw.println("      Interaction Learning: " + mInteractionLearning);
        
        for (String packageName : mUserProfiles.keySet()) {
            UserProfile profile = mUserProfiles.get(packageName);
            pw.println("      Profile: " + packageName + " (updated: " + profile.lastUpdated + ")");
        }
    }

    // Private methods

    private UserProfile createDefaultProfile(String packageName) {
        UserProfile profile = new UserProfile();
        profile.userId = packageName;
        profile.preferences = new Bundle();
        profile.learnedPatterns = new Bundle();
        profile.communicationStyle = new Bundle();
        profile.privacySettings = new Bundle();
        profile.automationPreferences = new Bundle();
        profile.lastUpdated = System.currentTimeMillis();
        
        // Set default preferences
        setDefaultPreferences(profile);
        setDefaultCommunicationStyle(profile);
        setDefaultPrivacySettings(profile);
        setDefaultAutomationPreferences(profile);
        
        return profile;
    }

    private void setDefaultPreferences(UserProfile profile) {
        // General preferences
        profile.preferences.putString("language", "en");
        profile.preferences.putString("timezone", "auto");
        profile.preferences.putBoolean("proactive_suggestions", true);
        profile.preferences.putInt("suggestion_frequency", 3); // 1=low, 3=medium, 5=high
        
        // AI behavior preferences
        profile.preferences.putString("ai_personality", "helpful"); // helpful, formal, casual
        profile.preferences.putBoolean("learning_enabled", true);
        profile.preferences.putBoolean("cross_app_learning", false);
    }

    private void setDefaultCommunicationStyle(UserProfile profile) {
        // Communication preferences
        profile.communicationStyle.putString("formality_level", "medium"); // formal, medium, casual
        profile.communicationStyle.putString("response_length", "medium"); // brief, medium, detailed
        profile.communicationStyle.putBoolean("use_emojis", false);
        profile.communicationStyle.putBoolean("technical_language", false);
        profile.communicationStyle.putString("preferred_greeting", "Hello");
    }

    private void setDefaultPrivacySettings(UserProfile profile) {
        // Privacy preferences
        profile.privacySettings.putBoolean("share_usage_data", false);
        profile.privacySettings.putBoolean("personalized_ads", false);
        profile.privacySettings.putInt("data_retention_days", 30);
        profile.privacySettings.putBoolean("cross_device_sync", false);
        profile.privacySettings.putString("privacy_level", "medium"); // low, medium, high
    }

    private void setDefaultAutomationPreferences(UserProfile profile) {
        // Automation preferences
        profile.automationPreferences.putBoolean("auto_execute_simple_tasks", false);
        profile.automationPreferences.putBoolean("confirm_before_actions", true);
        profile.automationPreferences.putInt("automation_aggressiveness", 2); // 1-5 scale
        profile.automationPreferences.putBoolean("learn_routines", true);
    }

    private void updateCommunicationStyle(UserProfile profile, UserInteraction interaction) {
        // Analyze interaction for communication style preferences
        String interactionType = interaction.interactionType;
        Bundle interactionData = interaction.interactionData;
        
        if ("voice_command".equals(interactionType)) {
            // Learn from voice interaction patterns
            updateVoiceCommunicationStyle(profile, interactionData);
        } else if ("text_input".equals(interactionType)) {
            // Learn from text interaction patterns
            updateTextCommunicationStyle(profile, interactionData);
        } else if ("feedback".equals(interactionType)) {
            // Learn from feedback patterns
            updateFeedbackCommunicationStyle(profile, interactionData);
        }
    }

    private void updateVoiceCommunicationStyle(UserProfile profile, Bundle data) {
        // Analyze voice patterns for formality, length preferences, etc.
        String voiceInput = data.getString("voice_input", "");
        
        // Simple heuristics for communication style learning
        if (voiceInput.contains("please") || voiceInput.contains("thank you")) {
            profile.communicationStyle.putString("formality_level", "formal");
        } else if (voiceInput.contains("hey") || voiceInput.contains("yo")) {
            profile.communicationStyle.putString("formality_level", "casual");
        }
        
        // Learn response length preference from user corrections
        String userFeedback = data.getString("feedback", "");
        if (userFeedback.contains("too long") || userFeedback.contains("brief")) {
            profile.communicationStyle.putString("response_length", "brief");
        } else if (userFeedback.contains("more detail") || userFeedback.contains("explain")) {
            profile.communicationStyle.putString("response_length", "detailed");
        }
    }

    private void updateTextCommunicationStyle(UserProfile profile, Bundle data) {
        // Analyze text patterns
        String textInput = data.getString("text_input", "");
        
        // Learn emoji usage preference
        boolean hasEmojis = textInput.matches(".*[\\p{So}\\p{Cn}].*");
        if (hasEmojis) {
            profile.communicationStyle.putBoolean("use_emojis", true);
        }
        
        // Learn technical language preference
        boolean hasTechnicalTerms = textInput.matches(".*(API|SDK|algorithm|database|server).*");
        if (hasTechnicalTerms) {
            profile.communicationStyle.putBoolean("technical_language", true);
        }
    }

    private void updateFeedbackCommunicationStyle(UserProfile profile, Bundle data) {
        // Learn from user feedback about AI responses
        int rating = data.getInt("rating", 0);
        String comments = data.getString("comments", "");
        
        if (rating >= 4 && comments.contains("perfect")) {
            // Current style is working well, reinforce it
            return;
        } else if (rating <= 2) {
            // Adjust style based on negative feedback
            if (comments.contains("too formal")) {
                profile.communicationStyle.putString("formality_level", "casual");
            } else if (comments.contains("too casual")) {
                profile.communicationStyle.putString("formality_level", "formal");
            }
        }
    }

    private void updateBehavioralPatterns(UserProfile profile, UserInteraction interaction) {
        // Learn behavioral patterns from interactions
        String pattern = detectBehavioralPattern(interaction);
        if (pattern != null) {
            long currentTime = System.currentTimeMillis();
            profile.learnedPatterns.putLong(pattern + "_last_seen", currentTime);
            
            int frequency = profile.learnedPatterns.getInt(pattern + "_frequency", 0);
            profile.learnedPatterns.putInt(pattern + "_frequency", frequency + 1);
        }
    }

    private String detectBehavioralPattern(UserInteraction interaction) {
        // Simple pattern detection based on interaction timing and type
        long hour = (System.currentTimeMillis() / (1000 * 60 * 60)) % 24;
        String interactionType = interaction.interactionType;
        
        if (hour >= 9 && hour <= 17 && "work_task".equals(interactionType)) {
            return "work_hours_productivity";
        } else if (hour >= 18 && hour <= 22 && "entertainment".equals(interactionType)) {
            return "evening_entertainment";
        } else if (hour >= 6 && hour <= 9 && "routine_task".equals(interactionType)) {
            return "morning_routine";
        }
        
        return null;
    }

    private void updatePreferencesFromInteraction(UserProfile profile, UserInteraction interaction) {
        // Update preferences based on interaction outcomes
        String outcome = interaction.outcome;
        
        if ("successful".equals(outcome)) {
            // Reinforce current preferences
            int currentAggressiveness = profile.automationPreferences.getInt("automation_aggressiveness", 2);
            if (currentAggressiveness < 5) {
                profile.automationPreferences.putInt("automation_aggressiveness", currentAggressiveness + 1);
            }
        } else if ("failed".equals(outcome) || "rejected".equals(outcome)) {
            // Reduce automation aggressiveness
            int currentAggressiveness = profile.automationPreferences.getInt("automation_aggressiveness", 2);
            if (currentAggressiveness > 1) {
                profile.automationPreferences.putInt("automation_aggressiveness", currentAggressiveness - 1);
            }
        }
    }

    private void saveUserProfile(String packageName, UserProfile profile) {
        // Save profile to secure storage
        Bundle profileBundle = new Bundle();
        profileBundle.putBundle("preferences", profile.preferences);
        profileBundle.putBundle("communication_style", profile.communicationStyle);
        profileBundle.putBundle("learned_patterns", profile.learnedPatterns);
        profileBundle.putBundle("privacy_settings", profile.privacySettings);
        profileBundle.putBundle("automation_preferences", profile.automationPreferences);
        profileBundle.putLong("last_updated", profile.lastUpdated);
        
        mPreferenceStorage.saveUserProfile(packageName, profileBundle);
        mProfileUpdates++;
    }
}
