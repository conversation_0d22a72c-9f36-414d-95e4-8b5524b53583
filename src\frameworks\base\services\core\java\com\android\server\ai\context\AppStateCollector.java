/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.context;

import android.app.ActivityManager;
import android.app.usage.UsageStats;
import android.app.usage.UsageStatsManager;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.util.Log;
import android.util.Slog;

import com.android.server.ai.security.AiSecurityManager;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Collects application state context for AI services in Jarvis OS.
 * 
 * Monitors current app, recent app usage, app states, and user interaction patterns
 * with applications while respecting privacy controls.
 */
public class AppStateCollector {
    private static final String TAG = "AppStateCollector";
    private static final boolean DEBUG = true;

    // Collection intervals
    private static final long APP_STATE_CHECK_INTERVAL_MS = 2000; // 2 seconds
    private static final long USAGE_STATS_WINDOW_MS = 24 * 60 * 60 * 1000; // 24 hours

    private final Context mContext;
    private final AiSecurityManager mSecurityManager;
    private final ActivityManager mActivityManager;
    private final UsageStatsManager mUsageStatsManager;
    private final PackageManager mPackageManager;
    
    // Collection state
    private final AtomicBoolean mCollectionEnabled = new AtomicBoolean(false);
    private final AtomicBoolean mFullMonitoringEnabled = new AtomicBoolean(false);
    
    // Current state
    private String mCurrentApp;
    private String mCurrentActivity;
    private long mLastStateUpdate = 0;
    private final List<AppUsageInfo> mRecentApps = new ArrayList<>();
    
    // Statistics
    private long mStateUpdatesCount = 0;
    private long mTotalCollectionTime = 0;

    public AppStateCollector(Context context, AiSecurityManager securityManager) {
        mContext = context;
        mSecurityManager = securityManager;
        mActivityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        mUsageStatsManager = (UsageStatsManager) context.getSystemService(Context.USAGE_STATS_SERVICE);
        mPackageManager = context.getPackageManager();
        
        if (DEBUG) Slog.d(TAG, "AppStateCollector initialized");
    }

    /**
     * Start app state collection
     */
    public void startCollection() {
        if (mCollectionEnabled.compareAndSet(false, true)) {
            if (DEBUG) Slog.d(TAG, "Starting app state collection");
            updateCurrentAppState();
        }
    }

    /**
     * Stop app state collection
     */
    public void stopCollection() {
        if (mCollectionEnabled.compareAndSet(true, false)) {
            if (DEBUG) Slog.d(TAG, "Stopping app state collection");
        }
    }

    /**
     * Initialize system service connections
     */
    public void initializeSystemServiceConnections() {
        if (DEBUG) Slog.d(TAG, "Initializing system service connections");
        // System services are already initialized in constructor
        // This method is for future extensions
    }

    /**
     * Enable full monitoring
     */
    public void enableFullMonitoring() {
        if (mFullMonitoringEnabled.compareAndSet(false, true)) {
            if (DEBUG) Slog.d(TAG, "Enabling full app monitoring");
            updateRecentAppsUsage();
        }
    }

    /**
     * Enable/disable collection
     */
    public void setEnabled(boolean enabled) {
        if (enabled) {
            startCollection();
        } else {
            stopCollection();
        }
    }

    /**
     * Collect current app state context
     */
    public Bundle collectContext() {
        if (!mCollectionEnabled.get()) {
            return null;
        }

        long startTime = System.currentTimeMillis();
        
        try {
            Bundle context = new Bundle();
            
            // Update current app state
            updateCurrentAppState();
            
            // Add current app information
            if (mCurrentApp != null) {
                context.putString("current_app", mCurrentApp);
                context.putString("current_app_label", getAppLabel(mCurrentApp));
                context.putString("current_app_category", getAppCategory(mCurrentApp));
            }
            
            if (mCurrentActivity != null) {
                context.putString("current_activity", mCurrentActivity);
            }
            
            // Add recent apps if full monitoring is enabled
            if (mFullMonitoringEnabled.get()) {
                updateRecentAppsUsage();
                addRecentAppsToContext(context);
            }
            
            // Add app state metadata
            context.putLong("last_state_update", mLastStateUpdate);
            context.putLong("collection_timestamp", System.currentTimeMillis());
            context.putBoolean("full_monitoring", mFullMonitoringEnabled.get());
            
            mStateUpdatesCount++;
            
            if (DEBUG) Slog.d(TAG, "App state context collected for: " + mCurrentApp);
            
            return context;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error collecting app state context", e);
            return null;
        } finally {
            long collectionTime = System.currentTimeMillis() - startTime;
            mTotalCollectionTime += collectionTime;
        }
    }

    /**
     * Get collection statistics
     */
    public Bundle getStatistics() {
        Bundle stats = new Bundle();
        stats.putBoolean("collection_enabled", mCollectionEnabled.get());
        stats.putBoolean("full_monitoring_enabled", mFullMonitoringEnabled.get());
        stats.putString("current_app", mCurrentApp);
        stats.putString("current_activity", mCurrentActivity);
        stats.putLong("last_state_update", mLastStateUpdate);
        stats.putLong("state_updates_count", mStateUpdatesCount);
        stats.putLong("total_collection_time", mTotalCollectionTime);
        stats.putInt("recent_apps_count", mRecentApps.size());
        
        return stats;
    }

    /**
     * Dump collector state for debugging
     */
    public void dump(PrintWriter pw) {
        pw.println("    AppStateCollector State:");
        pw.println("      Collection Enabled: " + mCollectionEnabled.get());
        pw.println("      Full Monitoring: " + mFullMonitoringEnabled.get());
        pw.println("      Current App: " + mCurrentApp);
        pw.println("      Current Activity: " + mCurrentActivity);
        pw.println("      Last Update: " + mLastStateUpdate);
        pw.println("      Updates Count: " + mStateUpdatesCount);
        pw.println("      Recent Apps: " + mRecentApps.size());
        
        if (mStateUpdatesCount > 0) {
            pw.println("      Avg Collection Time: " + (mTotalCollectionTime / mStateUpdatesCount) + "ms");
        }
    }

    // Private methods

    private void updateCurrentAppState() {
        try {
            // Get current running tasks
            List<ActivityManager.RunningTaskInfo> runningTasks = 
                mActivityManager.getRunningTasks(1);
            
            if (!runningTasks.isEmpty()) {
                ActivityManager.RunningTaskInfo topTask = runningTasks.get(0);
                String newCurrentApp = topTask.topActivity.getPackageName();
                String newCurrentActivity = topTask.topActivity.getClassName();
                
                // Update if changed
                if (!newCurrentApp.equals(mCurrentApp) || !newCurrentActivity.equals(mCurrentActivity)) {
                    mCurrentApp = newCurrentApp;
                    mCurrentActivity = newCurrentActivity;
                    mLastStateUpdate = System.currentTimeMillis();
                    
                    // Log app state change for security audit
                    mSecurityManager.logSecurityEvent("APP_STATE_CHANGE", mCurrentApp, 
                        android.os.Process.myUid(), createAppStateBundle());
                    
                    if (DEBUG) Slog.d(TAG, "App state changed to: " + mCurrentApp);
                }
            }
            
        } catch (SecurityException e) {
            // Handle permission issues gracefully
            Slog.w(TAG, "Permission denied for app state collection", e);
        } catch (Exception e) {
            Slog.e(TAG, "Error updating app state", e);
        }
    }

    private void updateRecentAppsUsage() {
        try {
            long endTime = System.currentTimeMillis();
            long startTime = endTime - USAGE_STATS_WINDOW_MS;
            
            // Get usage stats
            SortedMap<Long, UsageStats> sortedStats = new TreeMap<>();
            List<UsageStats> usageStatsList = mUsageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY, startTime, endTime);
            
            // Sort by last time used
            for (UsageStats usageStats : usageStatsList) {
                sortedStats.put(usageStats.getLastTimeUsed(), usageStats);
            }
            
            // Update recent apps list
            mRecentApps.clear();
            int count = 0;
            for (UsageStats stats : sortedStats.values()) {
                if (count >= 10) break; // Limit to top 10 recent apps
                
                if (stats.getTotalTimeInForeground() > 0) {
                    AppUsageInfo appInfo = new AppUsageInfo(
                        stats.getPackageName(),
                        stats.getLastTimeUsed(),
                        stats.getTotalTimeInForeground()
                    );
                    mRecentApps.add(appInfo);
                    count++;
                }
            }
            
        } catch (Exception e) {
            Slog.e(TAG, "Error updating recent apps usage", e);
        }
    }

    private void addRecentAppsToContext(Bundle context) {
        if (mRecentApps.isEmpty()) {
            return;
        }
        
        ArrayList<Bundle> recentAppsList = new ArrayList<>();
        for (AppUsageInfo appInfo : mRecentApps) {
            Bundle appBundle = new Bundle();
            appBundle.putString("package_name", appInfo.packageName);
            appBundle.putString("app_label", getAppLabel(appInfo.packageName));
            appBundle.putString("app_category", getAppCategory(appInfo.packageName));
            appBundle.putLong("last_used", appInfo.lastTimeUsed);
            appBundle.putLong("total_time", appInfo.totalTimeInForeground);
            recentAppsList.add(appBundle);
        }
        
        context.putParcelableArrayList("recent_apps", recentAppsList);
    }

    private String getAppLabel(String packageName) {
        try {
            ApplicationInfo appInfo = mPackageManager.getApplicationInfo(packageName, 0);
            return mPackageManager.getApplicationLabel(appInfo).toString();
        } catch (PackageManager.NameNotFoundException e) {
            return packageName;
        }
    }

    private String getAppCategory(String packageName) {
        try {
            ApplicationInfo appInfo = mPackageManager.getApplicationInfo(packageName, 0);
            int category = appInfo.category;
            
            switch (category) {
                case ApplicationInfo.CATEGORY_GAME:
                    return "game";
                case ApplicationInfo.CATEGORY_AUDIO:
                    return "audio";
                case ApplicationInfo.CATEGORY_VIDEO:
                    return "video";
                case ApplicationInfo.CATEGORY_IMAGE:
                    return "image";
                case ApplicationInfo.CATEGORY_SOCIAL:
                    return "social";
                case ApplicationInfo.CATEGORY_NEWS:
                    return "news";
                case ApplicationInfo.CATEGORY_MAPS:
                    return "maps";
                case ApplicationInfo.CATEGORY_PRODUCTIVITY:
                    return "productivity";
                default:
                    return "other";
            }
        } catch (PackageManager.NameNotFoundException e) {
            return "unknown";
        }
    }

    private Bundle createAppStateBundle() {
        Bundle bundle = new Bundle();
        bundle.putString("current_app", mCurrentApp);
        bundle.putString("current_activity", mCurrentActivity);
        bundle.putLong("timestamp", mLastStateUpdate);
        return bundle;
    }

    // Inner classes

    private static class AppUsageInfo {
        final String packageName;
        final long lastTimeUsed;
        final long totalTimeInForeground;

        AppUsageInfo(String packageName, long lastTimeUsed, long totalTimeInForeground) {
            this.packageName = packageName;
            this.lastTimeUsed = lastTimeUsed;
            this.totalTimeInForeground = totalTimeInForeground;
        }
    }
}
