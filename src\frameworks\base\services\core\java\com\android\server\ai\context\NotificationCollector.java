/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.context;

import android.app.Notification;
import android.content.Context;
import android.os.Bundle;
import android.service.notification.NotificationListenerService;
import android.service.notification.StatusBarNotification;
import android.util.Log;
import android.util.Slog;

import com.android.server.ai.security.AiSecurityManager;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Collects notification context for AI services in Jarvis OS.
 * 
 * Monitors active notifications, extracts relevant content while respecting
 * privacy controls, and provides notification-based context information.
 */
public class NotificationCollector {
    private static final String TAG = "NotificationCollector";
    private static final boolean DEBUG = true;

    // Privacy levels for notification content
    private static final int PRIVACY_LEVEL_NONE = 0;      // No content extraction
    private static final int PRIVACY_LEVEL_METADATA = 1;  // Only metadata (count, apps)
    private static final int PRIVACY_LEVEL_SUMMARY = 2;   // Summary information
    private static final int PRIVACY_LEVEL_FULL = 3;      // Full content (with user consent)

    private final Context mContext;
    private final AiSecurityManager mSecurityManager;
    
    // Collection state
    private final AtomicBoolean mCollectionEnabled = new AtomicBoolean(false);
    private final AtomicBoolean mFullMonitoringEnabled = new AtomicBoolean(false);
    
    // Notification tracking
    private final ConcurrentHashMap<String, NotificationInfo> mActiveNotifications = new ConcurrentHashMap<>();
    private final List<NotificationInfo> mRecentNotifications = new ArrayList<>();
    
    // Privacy settings
    private int mPrivacyLevel = PRIVACY_LEVEL_METADATA;
    private final List<String> mAllowedPackages = new ArrayList<>();
    private final List<String> mBlockedPackages = new ArrayList<>();
    
    // Statistics
    private long mNotificationsProcessed = 0;
    private long mNotificationsFiltered = 0;
    private long mLastNotificationTime = 0;

    public NotificationCollector(Context context, AiSecurityManager securityManager) {
        mContext = context;
        mSecurityManager = securityManager;
        
        // Initialize default privacy settings
        initializePrivacySettings();
        
        if (DEBUG) Slog.d(TAG, "NotificationCollector initialized");
    }

    /**
     * Start notification collection
     */
    public void startCollection() {
        if (mCollectionEnabled.compareAndSet(false, true)) {
            if (DEBUG) Slog.d(TAG, "Starting notification collection");
            
            // Register notification listener
            registerNotificationListener();
        }
    }

    /**
     * Stop notification collection
     */
    public void stopCollection() {
        if (mCollectionEnabled.compareAndSet(true, false)) {
            if (DEBUG) Slog.d(TAG, "Stopping notification collection");
            
            // Unregister notification listener
            unregisterNotificationListener();
        }
    }

    /**
     * Initialize system service connections
     */
    public void initializeSystemServiceConnections() {
        if (DEBUG) Slog.d(TAG, "Initializing system service connections");
        // System services are already available through context
    }

    /**
     * Enable full monitoring
     */
    public void enableFullMonitoring() {
        if (mFullMonitoringEnabled.compareAndSet(false, true)) {
            if (DEBUG) Slog.d(TAG, "Enabling full notification monitoring");
            
            // Increase privacy level if user has consented
            if (mPrivacyLevel < PRIVACY_LEVEL_SUMMARY) {
                mPrivacyLevel = PRIVACY_LEVEL_SUMMARY;
            }
        }
    }

    /**
     * Enable/disable collection
     */
    public void setEnabled(boolean enabled) {
        if (enabled) {
            startCollection();
        } else {
            stopCollection();
        }
    }

    /**
     * Set privacy level for notification content extraction
     */
    public void setPrivacyLevel(int privacyLevel) {
        if (privacyLevel >= PRIVACY_LEVEL_NONE && privacyLevel <= PRIVACY_LEVEL_FULL) {
            mPrivacyLevel = privacyLevel;
            
            if (DEBUG) Slog.d(TAG, "Privacy level set to: " + privacyLevel);
        }
    }

    /**
     * Add package to allowed list for notification content
     */
    public void addAllowedPackage(String packageName) {
        if (!mAllowedPackages.contains(packageName)) {
            mAllowedPackages.add(packageName);
            
            if (DEBUG) Slog.d(TAG, "Package added to allowed list: " + packageName);
        }
    }

    /**
     * Add package to blocked list for notification content
     */
    public void addBlockedPackage(String packageName) {
        if (!mBlockedPackages.contains(packageName)) {
            mBlockedPackages.add(packageName);
            
            if (DEBUG) Slog.d(TAG, "Package added to blocked list: " + packageName);
        }
    }

    /**
     * Collect current notification context
     */
    public Bundle collectContext() {
        if (!mCollectionEnabled.get()) {
            return null;
        }

        try {
            Bundle context = new Bundle();
            
            // Add notification count and metadata
            context.putInt("active_count", mActiveNotifications.size());
            context.putInt("recent_count", mRecentNotifications.size());
            context.putLong("last_notification", mLastNotificationTime);
            
            // Add notification summary based on privacy level
            if (mPrivacyLevel >= PRIVACY_LEVEL_METADATA) {
                addNotificationMetadata(context);
            }
            
            if (mPrivacyLevel >= PRIVACY_LEVEL_SUMMARY) {
                addNotificationSummary(context);
            }
            
            if (mPrivacyLevel >= PRIVACY_LEVEL_FULL) {
                addNotificationContent(context);
            }
            
            // Add collection metadata
            context.putLong("collection_timestamp", System.currentTimeMillis());
            context.putInt("privacy_level", mPrivacyLevel);
            context.putBoolean("full_monitoring", mFullMonitoringEnabled.get());
            
            if (DEBUG) Slog.d(TAG, "Notification context collected: " + 
                mActiveNotifications.size() + " active notifications");
            
            return context;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error collecting notification context", e);
            return null;
        }
    }

    /**
     * Get collection statistics
     */
    public Bundle getStatistics() {
        Bundle stats = new Bundle();
        stats.putBoolean("collection_enabled", mCollectionEnabled.get());
        stats.putBoolean("full_monitoring_enabled", mFullMonitoringEnabled.get());
        stats.putInt("privacy_level", mPrivacyLevel);
        stats.putLong("notifications_processed", mNotificationsProcessed);
        stats.putLong("notifications_filtered", mNotificationsFiltered);
        stats.putLong("last_notification_time", mLastNotificationTime);
        stats.putInt("active_notifications", mActiveNotifications.size());
        stats.putInt("recent_notifications", mRecentNotifications.size());
        stats.putInt("allowed_packages", mAllowedPackages.size());
        stats.putInt("blocked_packages", mBlockedPackages.size());
        
        return stats;
    }

    /**
     * Dump collector state for debugging
     */
    public void dump(PrintWriter pw) {
        pw.println("    NotificationCollector State:");
        pw.println("      Collection Enabled: " + mCollectionEnabled.get());
        pw.println("      Full Monitoring: " + mFullMonitoringEnabled.get());
        pw.println("      Privacy Level: " + mPrivacyLevel);
        pw.println("      Active Notifications: " + mActiveNotifications.size());
        pw.println("      Recent Notifications: " + mRecentNotifications.size());
        pw.println("      Notifications Processed: " + mNotificationsProcessed);
        pw.println("      Notifications Filtered: " + mNotificationsFiltered);
        pw.println("      Allowed Packages: " + mAllowedPackages.size());
        pw.println("      Blocked Packages: " + mBlockedPackages.size());
    }

    // Private methods

    private void initializePrivacySettings() {
        // Set conservative defaults
        mPrivacyLevel = PRIVACY_LEVEL_METADATA;
        
        // Add commonly safe packages to allowed list
        mAllowedPackages.add("com.android.systemui");
        mAllowedPackages.add("com.android.settings");
        mAllowedPackages.add("com.android.calendar");
        
        // Add sensitive packages to blocked list
        mBlockedPackages.add("com.android.mms");
        mBlockedPackages.add("com.whatsapp");
        mBlockedPackages.add("com.facebook.messenger");
        mBlockedPackages.add("com.google.android.gm"); // Gmail
    }

    private void registerNotificationListener() {
        // In a real implementation, this would register with NotificationManagerService
        // For now, we'll simulate notification monitoring
        if (DEBUG) Slog.d(TAG, "Notification listener registered");
    }

    private void unregisterNotificationListener() {
        // Unregister from NotificationManagerService
        if (DEBUG) Slog.d(TAG, "Notification listener unregistered");
    }

    private void addNotificationMetadata(Bundle context) {
        // Add package-level metadata
        ConcurrentHashMap<String, Integer> packageCounts = new ConcurrentHashMap<>();
        ConcurrentHashMap<String, String> packageCategories = new ConcurrentHashMap<>();
        
        for (NotificationInfo notification : mActiveNotifications.values()) {
            String packageName = notification.packageName;
            packageCounts.put(packageName, packageCounts.getOrDefault(packageName, 0) + 1);
            packageCategories.put(packageName, getNotificationCategory(notification));
        }
        
        // Convert to bundle format
        Bundle packageData = new Bundle();
        for (String packageName : packageCounts.keySet()) {
            Bundle packageInfo = new Bundle();
            packageInfo.putInt("count", packageCounts.get(packageName));
            packageInfo.putString("category", packageCategories.get(packageName));
            packageData.putBundle(packageName, packageInfo);
        }
        
        context.putBundle("package_metadata", packageData);
    }

    private void addNotificationSummary(Bundle context) {
        // Add summarized notification information
        int urgentCount = 0;
        int socialCount = 0;
        int workCount = 0;
        int systemCount = 0;
        
        for (NotificationInfo notification : mActiveNotifications.values()) {
            String category = getNotificationCategory(notification);
            
            switch (category) {
                case "urgent":
                    urgentCount++;
                    break;
                case "social":
                    socialCount++;
                    break;
                case "work":
                    workCount++;
                    break;
                case "system":
                    systemCount++;
                    break;
            }
        }
        
        Bundle summary = new Bundle();
        summary.putInt("urgent_count", urgentCount);
        summary.putInt("social_count", socialCount);
        summary.putInt("work_count", workCount);
        summary.putInt("system_count", systemCount);
        
        context.putBundle("notification_summary", summary);
    }

    private void addNotificationContent(Bundle context) {
        // Add actual notification content (only for allowed packages)
        ArrayList<Bundle> notificationList = new ArrayList<>();
        
        for (NotificationInfo notification : mActiveNotifications.values()) {
            if (isPackageAllowed(notification.packageName)) {
                Bundle notificationBundle = new Bundle();
                notificationBundle.putString("package", notification.packageName);
                notificationBundle.putString("title", sanitizeText(notification.title));
                notificationBundle.putString("text", sanitizeText(notification.text));
                notificationBundle.putString("category", getNotificationCategory(notification));
                notificationBundle.putLong("timestamp", notification.timestamp);
                notificationBundle.putBoolean("ongoing", notification.ongoing);
                
                notificationList.add(notificationBundle);
            }
        }
        
        context.putParcelableArrayList("notification_content", notificationList);
    }

    private String getNotificationCategory(NotificationInfo notification) {
        String packageName = notification.packageName;
        
        // Categorize based on package name and content
        if (packageName.contains("phone") || packageName.contains("call")) {
            return "urgent";
        } else if (packageName.contains("message") || packageName.contains("chat") || 
                   packageName.contains("social")) {
            return "social";
        } else if (packageName.contains("email") || packageName.contains("calendar") ||
                   packageName.contains("work")) {
            return "work";
        } else if (packageName.startsWith("com.android.") || packageName.startsWith("android.")) {
            return "system";
        } else {
            return "other";
        }
    }

    private boolean isPackageAllowed(String packageName) {
        // Check if package is explicitly blocked
        if (mBlockedPackages.contains(packageName)) {
            return false;
        }
        
        // Check if package is explicitly allowed
        if (mAllowedPackages.contains(packageName)) {
            return true;
        }
        
        // Default policy based on privacy level
        return mPrivacyLevel >= PRIVACY_LEVEL_FULL;
    }

    private String sanitizeText(String text) {
        if (text == null) {
            return "";
        }
        
        // Remove sensitive information patterns
        String sanitized = text;
        
        // Remove phone numbers
        sanitized = sanitized.replaceAll("\\b\\d{3}-\\d{3}-\\d{4}\\b", "[PHONE]");
        
        // Remove email addresses
        sanitized = sanitized.replaceAll("\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b", "[EMAIL]");
        
        // Remove credit card numbers
        sanitized = sanitized.replaceAll("\\b\\d{4}\\s?\\d{4}\\s?\\d{4}\\s?\\d{4}\\b", "[CARD]");
        
        // Limit length
        if (sanitized.length() > 100) {
            sanitized = sanitized.substring(0, 97) + "...";
        }
        
        return sanitized;
    }

    /**
     * Called when a new notification is posted
     */
    public void onNotificationPosted(StatusBarNotification sbn) {
        if (!mCollectionEnabled.get()) {
            return;
        }
        
        try {
            NotificationInfo info = createNotificationInfo(sbn);
            
            // Check if we should process this notification
            if (shouldProcessNotification(info)) {
                mActiveNotifications.put(sbn.getKey(), info);
                addToRecentNotifications(info);
                mLastNotificationTime = System.currentTimeMillis();
                mNotificationsProcessed++;
                
                // Log for security audit
                mSecurityManager.logSecurityEvent("NOTIFICATION_COLLECTED", 
                    info.packageName, android.os.Process.myUid(), createNotificationBundle(info));
                
                if (DEBUG) Slog.d(TAG, "Notification collected: " + info.packageName);
            } else {
                mNotificationsFiltered++;
            }
            
        } catch (Exception e) {
            Slog.e(TAG, "Error processing notification", e);
        }
    }

    /**
     * Called when a notification is removed
     */
    public void onNotificationRemoved(StatusBarNotification sbn) {
        mActiveNotifications.remove(sbn.getKey());
        
        if (DEBUG) Slog.d(TAG, "Notification removed: " + sbn.getPackageName());
    }

    private NotificationInfo createNotificationInfo(StatusBarNotification sbn) {
        Notification notification = sbn.getNotification();
        
        NotificationInfo info = new NotificationInfo();
        info.key = sbn.getKey();
        info.packageName = sbn.getPackageName();
        info.timestamp = sbn.getPostTime();
        info.ongoing = notification.flags != 0 && (notification.flags & Notification.FLAG_ONGOING_EVENT) != 0;
        
        // Extract title and text safely
        Bundle extras = notification.extras;
        if (extras != null) {
            info.title = extras.getCharSequence(Notification.EXTRA_TITLE, "").toString();
            info.text = extras.getCharSequence(Notification.EXTRA_TEXT, "").toString();
        }
        
        return info;
    }

    private boolean shouldProcessNotification(NotificationInfo info) {
        // Check if package is blocked
        if (mBlockedPackages.contains(info.packageName)) {
            return false;
        }
        
        // Always process if privacy level is metadata only
        if (mPrivacyLevel <= PRIVACY_LEVEL_METADATA) {
            return true;
        }
        
        // Check if package is allowed for content extraction
        return isPackageAllowed(info.packageName);
    }

    private void addToRecentNotifications(NotificationInfo info) {
        synchronized (mRecentNotifications) {
            mRecentNotifications.add(info);
            
            // Keep only last 50 notifications
            if (mRecentNotifications.size() > 50) {
                mRecentNotifications.remove(0);
            }
        }
    }

    private Bundle createNotificationBundle(NotificationInfo info) {
        Bundle bundle = new Bundle();
        bundle.putString("package", info.packageName);
        bundle.putString("category", getNotificationCategory(info));
        bundle.putLong("timestamp", info.timestamp);
        bundle.putBoolean("ongoing", info.ongoing);
        return bundle;
    }

    // Inner classes

    private static class NotificationInfo {
        String key;
        String packageName;
        String title;
        String text;
        long timestamp;
        boolean ongoing;
    }
}
