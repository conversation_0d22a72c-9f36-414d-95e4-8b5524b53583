/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef JARVIS_AI_SECURITY_H
#define JARVIS_AI_SECURITY_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Jarvis OS AI Security Library
 * 
 * Provides secure cryptographic operations, key management, and
 * security validation for AI services.
 */

// Version information
#define AI_SECURITY_VERSION_MAJOR 1
#define AI_SECURITY_VERSION_MINOR 0
#define AI_SECURITY_VERSION_PATCH 0

// Return codes
typedef enum {
    AI_SECURITY_SUCCESS = 0,
    AI_SECURITY_ERROR_INVALID_PARAM = -1,
    AI_SECURITY_ERROR_OUT_OF_MEMORY = -2,
    AI_SECURITY_ERROR_CRYPTO_FAILED = -3,
    AI_SECURITY_ERROR_KEY_NOT_FOUND = -4,
    AI_SECURITY_ERROR_PERMISSION_DENIED = -5,
    AI_SECURITY_ERROR_INVALID_SIGNATURE = -6,
    AI_SECURITY_ERROR_HARDWARE_NOT_AVAILABLE = -7,
    AI_SECURITY_ERROR_KEYSTORE_FAILED = -8
} ai_security_result_t;

// Data classification levels
typedef enum {
    AI_DATA_LEVEL_PUBLIC = 1,
    AI_DATA_LEVEL_PERSONAL = 2,
    AI_DATA_LEVEL_SENSITIVE = 3,
    AI_DATA_LEVEL_CRITICAL = 4
} ai_data_level_t;

// Encryption algorithms
typedef enum {
    AI_ENCRYPTION_AES_256_GCM = 0,
    AI_ENCRYPTION_AES_128_GCM = 1,
    AI_ENCRYPTION_CHACHA20_POLY1305 = 2
} ai_encryption_algorithm_t;

// Key derivation functions
typedef enum {
    AI_KDF_PBKDF2_SHA256 = 0,
    AI_KDF_SCRYPT = 1,
    AI_KDF_ARGON2ID = 2
} ai_key_derivation_t;

// Secure storage types
typedef enum {
    AI_STORAGE_KEYSTORE = 0,      // Android Keystore
    AI_STORAGE_TEE = 1,           // Trusted Execution Environment
    AI_STORAGE_SECURE_ELEMENT = 2 // Hardware Secure Element
} ai_storage_type_t;

// Forward declarations
typedef struct ai_crypto_context ai_crypto_context_t;
typedef struct ai_key_handle ai_key_handle_t;
typedef struct ai_secure_buffer ai_secure_buffer_t;

// Cryptographic context configuration
typedef struct {
    ai_encryption_algorithm_t algorithm;
    ai_key_derivation_t kdf;
    ai_storage_type_t key_storage;
    bool use_hardware_backing;
    bool require_user_authentication;
    uint32_t auth_timeout_seconds;
} ai_crypto_config_t;

// Key generation parameters
typedef struct {
    size_t key_size_bits;
    ai_data_level_t protection_level;
    bool exportable;
    bool require_user_presence;
    const char* key_alias;
    uint64_t validity_duration_seconds;
} ai_key_params_t;

// Secure buffer for sensitive data
struct ai_secure_buffer {
    void* data;
    size_t size;
    size_t capacity;
    bool is_locked;
    ai_data_level_t protection_level;
};

// Encryption/Decryption parameters
typedef struct {
    const uint8_t* iv;            // Initialization vector
    size_t iv_size;
    const uint8_t* aad;           // Additional authenticated data
    size_t aad_size;
    uint8_t* tag;                 // Authentication tag (output for encrypt, input for decrypt)
    size_t tag_size;
} ai_cipher_params_t;

/**
 * Initialize the AI security library
 * 
 * @param config Configuration for security operations
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_security_init(const ai_crypto_config_t* config);

/**
 * Cleanup and shutdown the AI security library
 */
void ai_security_cleanup(void);

/**
 * Get library version information
 * 
 * @param major Pointer to store major version
 * @param minor Pointer to store minor version
 * @param patch Pointer to store patch version
 */
void ai_security_get_version(int* major, int* minor, int* patch);

/**
 * Generate a new cryptographic key
 * 
 * @param params Key generation parameters
 * @param key_handle Pointer to store the generated key handle
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_key_generate(const ai_key_params_t* params,
                                    ai_key_handle_t** key_handle);

/**
 * Load an existing key by alias
 * 
 * @param key_alias Alias of the key to load
 * @param key_handle Pointer to store the loaded key handle
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_key_load(const char* key_alias,
                                ai_key_handle_t** key_handle);

/**
 * Delete a key from secure storage
 * 
 * @param key_alias Alias of the key to delete
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_key_delete(const char* key_alias);

/**
 * Release a key handle
 * 
 * @param key_handle The key handle to release
 */
void ai_key_release(ai_key_handle_t* key_handle);

/**
 * Create a cryptographic context for encryption/decryption
 * 
 * @param key_handle Key to use for operations
 * @param config Cryptographic configuration
 * @param context Pointer to store the created context
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_crypto_context_create(const ai_key_handle_t* key_handle,
                                             const ai_crypto_config_t* config,
                                             ai_crypto_context_t** context);

/**
 * Destroy a cryptographic context
 * 
 * @param context The context to destroy
 */
void ai_crypto_context_destroy(ai_crypto_context_t* context);

/**
 * Encrypt data using the cryptographic context
 * 
 * @param context The cryptographic context
 * @param plaintext Input plaintext data
 * @param plaintext_size Size of plaintext in bytes
 * @param params Cipher parameters (IV, AAD, etc.)
 * @param ciphertext Output buffer for encrypted data
 * @param ciphertext_size Size of ciphertext buffer
 * @param actual_size Pointer to store actual encrypted data size
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_encrypt(ai_crypto_context_t* context,
                               const uint8_t* plaintext,
                               size_t plaintext_size,
                               const ai_cipher_params_t* params,
                               uint8_t* ciphertext,
                               size_t ciphertext_size,
                               size_t* actual_size);

/**
 * Decrypt data using the cryptographic context
 * 
 * @param context The cryptographic context
 * @param ciphertext Input encrypted data
 * @param ciphertext_size Size of ciphertext in bytes
 * @param params Cipher parameters (IV, AAD, tag, etc.)
 * @param plaintext Output buffer for decrypted data
 * @param plaintext_size Size of plaintext buffer
 * @param actual_size Pointer to store actual decrypted data size
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_decrypt(ai_crypto_context_t* context,
                               const uint8_t* ciphertext,
                               size_t ciphertext_size,
                               const ai_cipher_params_t* params,
                               uint8_t* plaintext,
                               size_t plaintext_size,
                               size_t* actual_size);

/**
 * Create a secure buffer for sensitive data
 * 
 * @param size Size of the buffer in bytes
 * @param protection_level Data protection level
 * @param buffer Pointer to store the created secure buffer
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_secure_buffer_create(size_t size,
                                            ai_data_level_t protection_level,
                                            ai_secure_buffer_t** buffer);

/**
 * Destroy a secure buffer and securely wipe its contents
 * 
 * @param buffer The secure buffer to destroy
 */
void ai_secure_buffer_destroy(ai_secure_buffer_t* buffer);

/**
 * Lock a secure buffer in memory (prevent swapping)
 * 
 * @param buffer The secure buffer to lock
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_secure_buffer_lock(ai_secure_buffer_t* buffer);

/**
 * Unlock a secure buffer
 * 
 * @param buffer The secure buffer to unlock
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_secure_buffer_unlock(ai_secure_buffer_t* buffer);

/**
 * Securely wipe memory contents
 * 
 * @param ptr Pointer to memory to wipe
 * @param size Size of memory to wipe in bytes
 */
void ai_secure_memwipe(void* ptr, size_t size);

/**
 * Generate cryptographically secure random bytes
 * 
 * @param buffer Output buffer for random bytes
 * @param size Number of random bytes to generate
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_random_bytes(uint8_t* buffer, size_t size);

/**
 * Derive a key from a password using specified KDF
 * 
 * @param password Input password
 * @param password_size Size of password in bytes
 * @param salt Salt for key derivation
 * @param salt_size Size of salt in bytes
 * @param kdf Key derivation function to use
 * @param iterations Number of iterations (for PBKDF2)
 * @param derived_key Output buffer for derived key
 * @param key_size Size of derived key in bytes
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_derive_key(const uint8_t* password,
                                  size_t password_size,
                                  const uint8_t* salt,
                                  size_t salt_size,
                                  ai_key_derivation_t kdf,
                                  uint32_t iterations,
                                  uint8_t* derived_key,
                                  size_t key_size);

/**
 * Compute HMAC for message authentication
 * 
 * @param key HMAC key
 * @param key_size Size of HMAC key in bytes
 * @param message Input message
 * @param message_size Size of message in bytes
 * @param hmac Output buffer for HMAC
 * @param hmac_size Size of HMAC buffer (should be 32 for SHA-256)
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_hmac_sha256(const uint8_t* key,
                                   size_t key_size,
                                   const uint8_t* message,
                                   size_t message_size,
                                   uint8_t* hmac,
                                   size_t hmac_size);

/**
 * Verify HMAC for message authentication
 * 
 * @param key HMAC key
 * @param key_size Size of HMAC key in bytes
 * @param message Input message
 * @param message_size Size of message in bytes
 * @param expected_hmac Expected HMAC value
 * @param hmac_size Size of HMAC in bytes
 * @return AI_SECURITY_SUCCESS if HMAC is valid, error code otherwise
 */
ai_security_result_t ai_hmac_verify(const uint8_t* key,
                                   size_t key_size,
                                   const uint8_t* message,
                                   size_t message_size,
                                   const uint8_t* expected_hmac,
                                   size_t hmac_size);

/**
 * Check if hardware-backed security is available
 * 
 * @param storage_type Storage type to check
 * @return true if available, false otherwise
 */
bool ai_is_hardware_backed_available(ai_storage_type_t storage_type);

/**
 * Get available secure storage types
 * 
 * @param storage_types Array to store available storage types
 * @param max_count Maximum number of storage types to return
 * @param actual_count Pointer to store actual number of storage types
 * @return AI_SECURITY_SUCCESS on success, error code otherwise
 */
ai_security_result_t ai_get_available_storage_types(ai_storage_type_t* storage_types,
                                                   size_t max_count,
                                                   size_t* actual_count);

/**
 * Get error message for a result code
 * 
 * @param result The result code
 * @return Human-readable error message
 */
const char* ai_security_get_error_message(ai_security_result_t result);

/**
 * Enable or disable debug logging
 * 
 * @param enable true to enable debug logging, false to disable
 */
void ai_security_set_debug_logging(bool enable);

#ifdef __cplusplus
}
#endif

#endif // JARVIS_AI_SECURITY_H
