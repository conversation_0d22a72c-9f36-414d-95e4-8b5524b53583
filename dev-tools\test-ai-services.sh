#!/bin/bash

# Jarvis OS - AI Services Testing Script
# Quick testing and validation script for AI services development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo
    echo "=============================================="
    echo "🤖 Jarvis OS - AI Services Testing"
    echo "=============================================="
    echo
}

# Test compilation of Java sources
test_java_compilation() {
    print_status "Testing Java compilation..."
    
    local java_files=(
        "src/frameworks/base/services/core/java/com/android/server/ai/AiContextEngineService.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/AiPlanningOrchestrationService.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/security/AiSecurityManager.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/security/AiAuditLogger.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/security/AiEncryptionManager.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/context/ContextCollector.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/context/ContextDatabase.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/context/ContextFusion.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/context/AppStateCollector.java"
    )
    
    local missing_files=0
    local total_files=${#java_files[@]}
    
    for file in "${java_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "✓ $file"
        else
            print_error "✗ Missing: $file"
            ((missing_files++))
        fi
    done
    
    echo
    print_status "Java files: $((total_files - missing_files))/$total_files present"
    
    if [ $missing_files -eq 0 ]; then
        print_success "All Java source files present!"
        return 0
    else
        print_error "$missing_files Java source files missing"
        return 1
    fi
}

# Test AIDL interface files
test_aidl_interfaces() {
    print_status "Testing AIDL interfaces..."
    
    local aidl_file="docs/interfaces/ai-service-interfaces.aidl"
    
    if [ -f "$aidl_file" ]; then
        print_success "✓ AIDL interfaces defined"
        
        # Check for key interfaces
        local interfaces=(
            "IAiContextEngine"
            "IAiPlanningOrchestration"
            "IAiPersonalization"
            "ContextSnapshot"
            "TaskPlan"
            "ExecutionResult"
        )
        
        for interface in "${interfaces[@]}"; do
            if grep -q "$interface" "$aidl_file"; then
                print_success "  ✓ $interface interface found"
            else
                print_warning "  ⚠ $interface interface not found"
            fi
        done
        
        return 0
    else
        print_error "✗ AIDL interfaces file missing"
        return 1
    fi
}

# Test build configuration
test_build_config() {
    print_status "Testing build configuration..."
    
    local build_files=(
        "build/Android.bp"
        "build/aosp-setup.sh"
    )
    
    local missing_files=0
    
    for file in "${build_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "✓ $file"
        else
            print_error "✗ Missing: $file"
            ((missing_files++))
        fi
    done
    
    # Check Android.bp content
    if [ -f "build/Android.bp" ]; then
        local targets=(
            "jarvis-ai-services"
            "jarvis-ai-interfaces"
            "jarvis-ai-security"
            "libai_inference"
            "libai_security"
        )
        
        for target in "${targets[@]}"; do
            if grep -q "$target" "build/Android.bp"; then
                print_success "  ✓ Build target: $target"
            else
                print_warning "  ⚠ Build target missing: $target"
            fi
        done
    fi
    
    return $missing_files
}

# Test documentation completeness
test_documentation() {
    print_status "Testing documentation..."
    
    local doc_files=(
        "README.md"
        "docs/architecture/system-overview.md"
        "docs/architecture/aosp-modifications.md"
        "docs/security/privacy-design.md"
        "docs/examples/gemini-integration.md"
        "roadmap/development-phases.md"
        "JARVIS_OS_IMPLEMENTATION_GUIDE.md"
        "PHASE_1_STATUS.md"
    )
    
    local missing_docs=0
    local total_docs=${#doc_files[@]}
    
    for doc in "${doc_files[@]}"; do
        if [ -f "$doc" ]; then
            print_success "✓ $doc"
        else
            print_error "✗ Missing: $doc"
            ((missing_docs++))
        fi
    done
    
    echo
    print_status "Documentation: $((total_docs - missing_docs))/$total_docs files present"
    
    return $missing_docs
}

# Test security framework
test_security_framework() {
    print_status "Testing security framework..."
    
    local security_files=(
        "src/frameworks/base/services/core/java/com/android/server/ai/security/AiSecurityManager.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/security/AiAuditLogger.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/security/AiEncryptionManager.java"
    )
    
    local security_features=(
        "permission validation"
        "data classification"
        "encryption"
        "audit logging"
        "security policies"
    )
    
    local missing_files=0
    
    for file in "${security_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "✓ $(basename "$file")"
        else
            print_error "✗ Missing: $(basename "$file")"
            ((missing_files++))
        fi
    done
    
    # Check for security features in AiSecurityManager
    if [ -f "src/frameworks/base/services/core/java/com/android/server/ai/security/AiSecurityManager.java" ]; then
        local features_found=0
        
        if grep -q "checkPermission\|hasContextPermission" "src/frameworks/base/services/core/java/com/android/server/ai/security/AiSecurityManager.java"; then
            print_success "  ✓ Permission validation implemented"
            ((features_found++))
        fi
        
        if grep -q "classifyData\|DATA_LEVEL" "src/frameworks/base/services/core/java/com/android/server/ai/security/AiSecurityManager.java"; then
            print_success "  ✓ Data classification implemented"
            ((features_found++))
        fi
        
        if grep -q "encryptData\|decryptData" "src/frameworks/base/services/core/java/com/android/server/ai/security/AiSecurityManager.java"; then
            print_success "  ✓ Encryption framework implemented"
            ((features_found++))
        fi
        
        print_status "Security features: $features_found/${#security_features[@]} implemented"
    fi
    
    return $missing_files
}

# Test context collection framework
test_context_framework() {
    print_status "Testing context collection framework..."
    
    local context_files=(
        "src/frameworks/base/services/core/java/com/android/server/ai/context/ContextCollector.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/context/ContextDatabase.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/context/ContextFusion.java"
        "src/frameworks/base/services/core/java/com/android/server/ai/context/AppStateCollector.java"
    )
    
    local missing_files=0
    
    for file in "${context_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "✓ $(basename "$file")"
        else
            print_error "✗ Missing: $(basename "$file")"
            ((missing_files++))
        fi
    done
    
    return $missing_files
}

# Generate test report
generate_report() {
    local java_result=$1
    local aidl_result=$2
    local build_result=$3
    local docs_result=$4
    local security_result=$5
    local context_result=$6
    
    echo
    echo "=============================================="
    echo "📊 Test Results Summary"
    echo "=============================================="
    
    local total_score=0
    local max_score=6
    
    # Java compilation
    if [ $java_result -eq 0 ]; then
        print_success "✅ Java Sources: PASS"
        ((total_score++))
    else
        print_error "❌ Java Sources: FAIL"
    fi
    
    # AIDL interfaces
    if [ $aidl_result -eq 0 ]; then
        print_success "✅ AIDL Interfaces: PASS"
        ((total_score++))
    else
        print_error "❌ AIDL Interfaces: FAIL"
    fi
    
    # Build configuration
    if [ $build_result -eq 0 ]; then
        print_success "✅ Build Config: PASS"
        ((total_score++))
    else
        print_error "❌ Build Config: FAIL"
    fi
    
    # Documentation
    if [ $docs_result -eq 0 ]; then
        print_success "✅ Documentation: PASS"
        ((total_score++))
    else
        print_error "❌ Documentation: FAIL"
    fi
    
    # Security framework
    if [ $security_result -eq 0 ]; then
        print_success "✅ Security Framework: PASS"
        ((total_score++))
    else
        print_error "❌ Security Framework: FAIL"
    fi
    
    # Context framework
    if [ $context_result -eq 0 ]; then
        print_success "✅ Context Framework: PASS"
        ((total_score++))
    else
        print_error "❌ Context Framework: FAIL"
    fi
    
    echo
    print_status "Overall Score: $total_score/$max_score"
    
    if [ $total_score -eq $max_score ]; then
        print_success "🎉 All tests passed! Ready for next phase."
    elif [ $total_score -ge 4 ]; then
        print_warning "⚠️  Most tests passed. Address failing tests before proceeding."
    else
        print_error "❌ Multiple test failures. Significant work needed."
    fi
    
    echo
    echo "Next steps:"
    echo "1. Address any failing tests"
    echo "2. Complete remaining context collectors"
    echo "3. Implement AiPersonalizationService"
    echo "4. Begin native library development"
    echo
}

# Main execution
main() {
    print_header
    
    # Run all tests
    test_java_compilation
    java_result=$?
    
    echo
    test_aidl_interfaces
    aidl_result=$?
    
    echo
    test_build_config
    build_result=$?
    
    echo
    test_documentation
    docs_result=$?
    
    echo
    test_security_framework
    security_result=$?
    
    echo
    test_context_framework
    context_result=$?
    
    # Generate report
    generate_report $java_result $aidl_result $build_result $docs_result $security_result $context_result
}

# Run main function
main "$@"
