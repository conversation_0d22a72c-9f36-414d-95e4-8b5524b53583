/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.context;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.util.Log;
import android.util.Slog;

import com.android.server.ai.security.AiSecurityManager;

import java.io.PrintWriter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Context collector for AI services in Jarvis OS.
 * 
 * Collects context information from various system sources including
 * app states, notifications, sensors, and user interactions.
 */
public class ContextCollector {
    private static final String TAG = "ContextCollector";
    private static final boolean DEBUG = true;

    // Collection intervals
    private static final long CONTEXT_UPDATE_INTERVAL_MS = 5000; // 5 seconds
    private static final long SENSOR_UPDATE_INTERVAL_MS = 1000; // 1 second
    private static final long APP_STATE_UPDATE_INTERVAL_MS = 2000; // 2 seconds

    private final Context mContext;
    private final ContextFusion mContextFusion;
    private final Handler mHandler;
    private final AiSecurityManager mSecurityManager;
    
    // Context collectors
    private final AppStateCollector mAppStateCollector;
    private final NotificationCollector mNotificationCollector;
    private final SensorCollector mSensorCollector;
    private final UserInteractionCollector mUserInteractionCollector;
    private final EnvironmentalCollector mEnvironmentalCollector;
    
    // Collection state
    private final AtomicBoolean mCollectionEnabled = new AtomicBoolean(false);
    private final AtomicBoolean mFullMonitoringEnabled = new AtomicBoolean(false);
    private final ConcurrentHashMap<String, Boolean> mContextTypeEnabled = new ConcurrentHashMap<>();
    
    // Statistics
    private long mLastContextUpdate = 0;
    private int mContextUpdatesCount = 0;
    private long mTotalCollectionTime = 0;

    public ContextCollector(Context context, ContextFusion contextFusion, Handler handler) {
        mContext = context;
        mContextFusion = contextFusion;
        mHandler = handler;
        mSecurityManager = new AiSecurityManager(context);
        
        // Initialize collectors
        mAppStateCollector = new AppStateCollector(context, mSecurityManager);
        mNotificationCollector = new NotificationCollector(context, mSecurityManager);
        mSensorCollector = new SensorCollector(context, mSecurityManager);
        mUserInteractionCollector = new UserInteractionCollector(context, mSecurityManager);
        mEnvironmentalCollector = new EnvironmentalCollector(context, mSecurityManager);
        
        // Initialize context type enablement
        initializeContextTypes();
        
        if (DEBUG) Slog.d(TAG, "ContextCollector initialized");
    }

    /**
     * Start context collection
     */
    public void startCollection() {
        if (mCollectionEnabled.compareAndSet(false, true)) {
            if (DEBUG) Slog.d(TAG, "Starting context collection");
            
            // Start periodic context updates
            mHandler.post(mContextUpdateRunnable);
            
            // Initialize collectors
            mAppStateCollector.startCollection();
            mNotificationCollector.startCollection();
            mSensorCollector.startCollection();
            mUserInteractionCollector.startCollection();
            mEnvironmentalCollector.startCollection();
        }
    }

    /**
     * Stop context collection
     */
    public void stopCollection() {
        if (mCollectionEnabled.compareAndSet(true, false)) {
            if (DEBUG) Slog.d(TAG, "Stopping context collection");
            
            // Stop periodic updates
            mHandler.removeCallbacks(mContextUpdateRunnable);
            
            // Stop collectors
            mAppStateCollector.stopCollection();
            mNotificationCollector.stopCollection();
            mSensorCollector.stopCollection();
            mUserInteractionCollector.stopCollection();
            mEnvironmentalCollector.stopCollection();
        }
    }

    /**
     * Initialize connections to system services
     */
    public void initializeSystemServiceConnections() {
        if (DEBUG) Slog.d(TAG, "Initializing system service connections");
        
        mAppStateCollector.initializeSystemServiceConnections();
        mNotificationCollector.initializeSystemServiceConnections();
        mSensorCollector.initializeSystemServiceConnections();
        mUserInteractionCollector.initializeSystemServiceConnections();
        mEnvironmentalCollector.initializeSystemServiceConnections();
    }

    /**
     * Enable full monitoring (called after boot completed)
     */
    public void enableFullMonitoring() {
        if (mFullMonitoringEnabled.compareAndSet(false, true)) {
            if (DEBUG) Slog.d(TAG, "Enabling full monitoring");
            
            mAppStateCollector.enableFullMonitoring();
            mNotificationCollector.enableFullMonitoring();
            mSensorCollector.enableFullMonitoring();
            mUserInteractionCollector.enableFullMonitoring();
            mEnvironmentalCollector.enableFullMonitoring();
        }
    }

    /**
     * Enable/disable specific context type collection
     */
    public void setContextTypeEnabled(String contextType, boolean enabled) {
        mContextTypeEnabled.put(contextType, enabled);
        
        // Update collectors based on context type
        switch (contextType) {
            case AiSecurityManager.CONTEXT_APP_STATE:
                mAppStateCollector.setEnabled(enabled);
                break;
            case AiSecurityManager.CONTEXT_NOTIFICATIONS:
                mNotificationCollector.setEnabled(enabled);
                break;
            case AiSecurityManager.CONTEXT_SENSORS:
                mSensorCollector.setEnabled(enabled);
                break;
            case AiSecurityManager.CONTEXT_LOCATION:
                mEnvironmentalCollector.setLocationEnabled(enabled);
                break;
            case AiSecurityManager.CONTEXT_COMMUNICATION:
                mUserInteractionCollector.setCommunicationEnabled(enabled);
                break;
        }
        
        if (DEBUG) Slog.d(TAG, "Context type " + contextType + " set to: " + enabled);
    }

    /**
     * Check if context type is enabled
     */
    public boolean isContextTypeEnabled(String contextType) {
        return mContextTypeEnabled.getOrDefault(contextType, true);
    }

    /**
     * Get current collection statistics
     */
    public Bundle getCollectionStatistics() {
        Bundle stats = new Bundle();
        stats.putBoolean("collection_enabled", mCollectionEnabled.get());
        stats.putBoolean("full_monitoring_enabled", mFullMonitoringEnabled.get());
        stats.putLong("last_context_update", mLastContextUpdate);
        stats.putInt("context_updates_count", mContextUpdatesCount);
        stats.putLong("total_collection_time", mTotalCollectionTime);
        stats.putLong("average_collection_time", 
            mContextUpdatesCount > 0 ? mTotalCollectionTime / mContextUpdatesCount : 0);
        
        return stats;
    }

    /**
     * Dump collector state for debugging
     */
    public void dump(PrintWriter pw) {
        pw.println("  ContextCollector State:");
        pw.println("    Collection Enabled: " + mCollectionEnabled.get());
        pw.println("    Full Monitoring: " + mFullMonitoringEnabled.get());
        pw.println("    Last Update: " + mLastContextUpdate);
        pw.println("    Updates Count: " + mContextUpdatesCount);
        pw.println("    Avg Collection Time: " + 
            (mContextUpdatesCount > 0 ? mTotalCollectionTime / mContextUpdatesCount : 0) + "ms");
        
        pw.println("    Context Types:");
        for (String contextType : mContextTypeEnabled.keySet()) {
            pw.println("      " + contextType + ": " + mContextTypeEnabled.get(contextType));
        }
        
        // Dump individual collectors
        mAppStateCollector.dump(pw);
        mNotificationCollector.dump(pw);
        mSensorCollector.dump(pw);
        mUserInteractionCollector.dump(pw);
        mEnvironmentalCollector.dump(pw);
    }

    // Private methods

    private void initializeContextTypes() {
        // Enable all context types by default
        mContextTypeEnabled.put(AiSecurityManager.CONTEXT_APP_STATE, true);
        mContextTypeEnabled.put(AiSecurityManager.CONTEXT_NOTIFICATIONS, true);
        mContextTypeEnabled.put(AiSecurityManager.CONTEXT_LOCATION, true);
        mContextTypeEnabled.put(AiSecurityManager.CONTEXT_SENSORS, true);
        mContextTypeEnabled.put(AiSecurityManager.CONTEXT_COMMUNICATION, false); // Disabled by default for privacy
        mContextTypeEnabled.put(AiSecurityManager.CONTEXT_CALENDAR, true);
        mContextTypeEnabled.put(AiSecurityManager.CONTEXT_CONTACTS, false); // Disabled by default for privacy
    }

    private final Runnable mContextUpdateRunnable = new Runnable() {
        @Override
        public void run() {
            if (!mCollectionEnabled.get()) {
                return;
            }

            long startTime = SystemClock.elapsedRealtime();
            
            try {
                // Collect context from all sources
                Bundle contextData = collectCurrentContext();
                
                // Send to context fusion
                if (contextData != null) {
                    mContextFusion.processContextUpdate(contextData);
                    mLastContextUpdate = System.currentTimeMillis();
                    mContextUpdatesCount++;
                }
                
            } catch (Exception e) {
                Slog.e(TAG, "Error during context collection", e);
            } finally {
                long collectionTime = SystemClock.elapsedRealtime() - startTime;
                mTotalCollectionTime += collectionTime;
                
                if (DEBUG && collectionTime > 100) {
                    Slog.w(TAG, "Context collection took " + collectionTime + "ms");
                }
                
                // Schedule next update
                mHandler.postDelayed(this, CONTEXT_UPDATE_INTERVAL_MS);
            }
        }
    };

    private Bundle collectCurrentContext() {
        Bundle contextData = new Bundle();
        contextData.putLong("timestamp", System.currentTimeMillis());
        
        // Collect from each source if enabled
        if (isContextTypeEnabled(AiSecurityManager.CONTEXT_APP_STATE)) {
            Bundle appState = mAppStateCollector.collectContext();
            if (appState != null) {
                contextData.putBundle("app_state", appState);
            }
        }
        
        if (isContextTypeEnabled(AiSecurityManager.CONTEXT_NOTIFICATIONS)) {
            Bundle notifications = mNotificationCollector.collectContext();
            if (notifications != null) {
                contextData.putBundle("notifications", notifications);
            }
        }
        
        if (isContextTypeEnabled(AiSecurityManager.CONTEXT_SENSORS)) {
            Bundle sensors = mSensorCollector.collectContext();
            if (sensors != null) {
                contextData.putBundle("sensors", sensors);
            }
        }
        
        if (isContextTypeEnabled(AiSecurityManager.CONTEXT_LOCATION)) {
            Bundle location = mEnvironmentalCollector.collectLocationContext();
            if (location != null) {
                contextData.putBundle("location", location);
            }
        }
        
        if (isContextTypeEnabled(AiSecurityManager.CONTEXT_COMMUNICATION)) {
            Bundle communication = mUserInteractionCollector.collectCommunicationContext();
            if (communication != null) {
                contextData.putBundle("communication", communication);
            }
        }
        
        if (isContextTypeEnabled(AiSecurityManager.CONTEXT_CALENDAR)) {
            Bundle calendar = mEnvironmentalCollector.collectCalendarContext();
            if (calendar != null) {
                contextData.putBundle("calendar", calendar);
            }
        }
        
        return contextData;
    }
}
