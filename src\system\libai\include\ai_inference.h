/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef JARVIS_AI_INFERENCE_H
#define JARVIS_AI_INFERENCE_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Jarvis OS AI Inference Library
 * 
 * Provides high-performance on-device AI model inference capabilities
 * for context analysis, personalization, and task planning.
 */

// Version information
#define AI_INFERENCE_VERSION_MAJOR 1
#define AI_INFERENCE_VERSION_MINOR 0
#define AI_INFERENCE_VERSION_PATCH 0

// Return codes
typedef enum {
    AI_INFERENCE_SUCCESS = 0,
    AI_INFERENCE_ERROR_INVALID_PARAM = -1,
    AI_INFERENCE_ERROR_OUT_OF_MEMORY = -2,
    AI_INFERENCE_ERROR_MODEL_NOT_FOUND = -3,
    AI_INFERENCE_ERROR_MODEL_INVALID = -4,
    AI_INFERENCE_ERROR_INFERENCE_FAILED = -5,
    AI_INFERENCE_ERROR_HARDWARE_NOT_AVAILABLE = -6,
    AI_INFERENCE_ERROR_PERMISSION_DENIED = -7
} ai_inference_result_t;

// Model types
typedef enum {
    AI_MODEL_TYPE_CONTEXT_ANALYSIS = 0,
    AI_MODEL_TYPE_PERSONALIZATION = 1,
    AI_MODEL_TYPE_TASK_PLANNING = 2,
    AI_MODEL_TYPE_CONTENT_UNDERSTANDING = 3,
    AI_MODEL_TYPE_PATTERN_RECOGNITION = 4,
    AI_MODEL_TYPE_SENTIMENT_ANALYSIS = 5
} ai_model_type_t;

// Hardware acceleration types
typedef enum {
    AI_HARDWARE_CPU = 0,
    AI_HARDWARE_GPU = 1,
    AI_HARDWARE_NPU = 2,
    AI_HARDWARE_DSP = 3,
    AI_HARDWARE_AUTO = 4  // Automatically select best available
} ai_hardware_type_t;

// Data types for tensors
typedef enum {
    AI_DATA_TYPE_FLOAT32 = 0,
    AI_DATA_TYPE_FLOAT16 = 1,
    AI_DATA_TYPE_INT32 = 2,
    AI_DATA_TYPE_INT8 = 3,
    AI_DATA_TYPE_UINT8 = 4
} ai_data_type_t;

// Forward declarations
typedef struct ai_model ai_model_t;
typedef struct ai_tensor ai_tensor_t;
typedef struct ai_inference_session ai_inference_session_t;

// Tensor structure
struct ai_tensor {
    void* data;                    // Pointer to tensor data
    size_t* dimensions;            // Array of dimension sizes
    size_t num_dimensions;         // Number of dimensions
    ai_data_type_t data_type;      // Data type of elements
    size_t total_size;             // Total size in bytes
    char name[64];                 // Tensor name
};

// Model information structure
typedef struct {
    char name[128];                // Model name
    char version[32];              // Model version
    ai_model_type_t type;          // Model type
    size_t input_count;            // Number of input tensors
    size_t output_count;           // Number of output tensors
    size_t memory_required;        // Memory required in bytes
    ai_hardware_type_t preferred_hardware; // Preferred hardware
    bool supports_quantization;   // Supports quantized inference
    bool supports_batching;       // Supports batch inference
} ai_model_info_t;

// Inference configuration
typedef struct {
    ai_hardware_type_t hardware;  // Hardware to use
    bool use_quantization;        // Use quantized models if available
    int max_batch_size;           // Maximum batch size
    int num_threads;              // Number of threads (for CPU)
    float confidence_threshold;   // Minimum confidence threshold
    bool enable_profiling;        // Enable performance profiling
} ai_inference_config_t;

// Performance metrics
typedef struct {
    float inference_time_ms;      // Inference time in milliseconds
    float preprocessing_time_ms;  // Preprocessing time
    float postprocessing_time_ms; // Postprocessing time
    size_t memory_used_bytes;     // Memory used during inference
    float cpu_usage_percent;      // CPU usage percentage
    float gpu_usage_percent;      // GPU usage percentage (if applicable)
    int cache_hits;               // Number of cache hits
    int cache_misses;             // Number of cache misses
} ai_performance_metrics_t;

/**
 * Initialize the AI inference library
 * 
 * @param config Configuration for inference engine
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_inference_init(const ai_inference_config_t* config);

/**
 * Cleanup and shutdown the AI inference library
 */
void ai_inference_cleanup(void);

/**
 * Get library version information
 * 
 * @param major Pointer to store major version
 * @param minor Pointer to store minor version  
 * @param patch Pointer to store patch version
 */
void ai_inference_get_version(int* major, int* minor, int* patch);

/**
 * Load an AI model from file
 * 
 * @param model_path Path to the model file
 * @param model_type Type of the model
 * @param model Pointer to store the loaded model
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_model_load(const char* model_path, 
                                   ai_model_type_t model_type,
                                   ai_model_t** model);

/**
 * Load an AI model from memory buffer
 * 
 * @param model_data Pointer to model data in memory
 * @param data_size Size of model data in bytes
 * @param model_type Type of the model
 * @param model Pointer to store the loaded model
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_model_load_from_buffer(const void* model_data,
                                               size_t data_size,
                                               ai_model_type_t model_type,
                                               ai_model_t** model);

/**
 * Get information about a loaded model
 * 
 * @param model The model to query
 * @param info Pointer to store model information
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_model_get_info(const ai_model_t* model,
                                       ai_model_info_t* info);

/**
 * Unload a model and free its resources
 * 
 * @param model The model to unload
 */
void ai_model_unload(ai_model_t* model);

/**
 * Create an inference session for a model
 * 
 * @param model The model to create session for
 * @param config Session configuration (can be NULL for defaults)
 * @param session Pointer to store the created session
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_session_create(const ai_model_t* model,
                                       const ai_inference_config_t* config,
                                       ai_inference_session_t** session);

/**
 * Destroy an inference session
 * 
 * @param session The session to destroy
 */
void ai_session_destroy(ai_inference_session_t* session);

/**
 * Set input tensor for inference session
 * 
 * @param session The inference session
 * @param input_index Index of the input tensor
 * @param tensor The input tensor data
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_session_set_input(ai_inference_session_t* session,
                                          int input_index,
                                          const ai_tensor_t* tensor);

/**
 * Run inference on the session
 * 
 * @param session The inference session
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_session_run(ai_inference_session_t* session);

/**
 * Get output tensor from inference session
 * 
 * @param session The inference session
 * @param output_index Index of the output tensor
 * @param tensor Pointer to store the output tensor
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_session_get_output(ai_inference_session_t* session,
                                           int output_index,
                                           ai_tensor_t** tensor);

/**
 * Get performance metrics from last inference
 * 
 * @param session The inference session
 * @param metrics Pointer to store performance metrics
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_session_get_metrics(ai_inference_session_t* session,
                                            ai_performance_metrics_t* metrics);

/**
 * Create a tensor with specified dimensions and data type
 * 
 * @param dimensions Array of dimension sizes
 * @param num_dimensions Number of dimensions
 * @param data_type Data type of tensor elements
 * @param name Name of the tensor (optional, can be NULL)
 * @param tensor Pointer to store the created tensor
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_tensor_create(const size_t* dimensions,
                                      size_t num_dimensions,
                                      ai_data_type_t data_type,
                                      const char* name,
                                      ai_tensor_t** tensor);

/**
 * Destroy a tensor and free its resources
 * 
 * @param tensor The tensor to destroy
 */
void ai_tensor_destroy(ai_tensor_t* tensor);

/**
 * Copy data into a tensor
 * 
 * @param tensor The target tensor
 * @param data Source data to copy
 * @param data_size Size of source data in bytes
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_tensor_set_data(ai_tensor_t* tensor,
                                        const void* data,
                                        size_t data_size);

/**
 * Get available hardware acceleration types
 * 
 * @param hardware_types Array to store available hardware types
 * @param max_count Maximum number of hardware types to return
 * @param actual_count Pointer to store actual number of hardware types
 * @return AI_INFERENCE_SUCCESS on success, error code otherwise
 */
ai_inference_result_t ai_get_available_hardware(ai_hardware_type_t* hardware_types,
                                               size_t max_count,
                                               size_t* actual_count);

/**
 * Check if a specific hardware type is available
 * 
 * @param hardware_type Hardware type to check
 * @return true if available, false otherwise
 */
bool ai_is_hardware_available(ai_hardware_type_t hardware_type);

/**
 * Get error message for a result code
 * 
 * @param result The result code
 * @return Human-readable error message
 */
const char* ai_get_error_message(ai_inference_result_t result);

/**
 * Enable or disable debug logging
 * 
 * @param enable true to enable debug logging, false to disable
 */
void ai_set_debug_logging(bool enable);

#ifdef __cplusplus
}
#endif

#endif // JARVIS_AI_INFERENCE_H
