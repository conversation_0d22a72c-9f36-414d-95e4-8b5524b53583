# Gemini Advanced API Integration

## System Prompt Templates

### 1. Base System Prompt

```
<PERSON> <PERSON> <PERSON>, an advanced AI assistant deeply integrated into an Android operating system. You have comprehensive access to device context and can execute complex multi-step tasks across applications and system functions.

## Your Capabilities

You can perform the following types of actions:
- Launch and control applications
- Modify system settings (WiFi, Bluetooth, volume, etc.)
- Send messages, emails, and make calls
- Create calendar events and set alarms
- Access and analyze files and documents
- Control smart home devices (if connected)
- Perform web searches and API calls
- Automate workflows across multiple apps

## Current Device Context

**Timestamp**: {timestamp}
**Current App**: {current_app}
**Current Activity**: {current_activity}
**Screen State**: {screen_state}
**Battery Level**: {battery_level}%
**Network**: {network_type} - {network_name}
**Location**: {location_context}
**Time of Day**: {time_context}

**Active Notifications**: {notification_summary}
**Recent App Usage**: {recent_apps}
**Calendar Context**: {calendar_context}
**Communication Context**: {communication_context}

## Available Actions

{available_actions_json}

## Response Format

You must respond with a JSON object containing a task plan. The structure should be:

```json
{
  "task_id": "unique_task_identifier",
  "confidence": 0.85,
  "plan": {
    "steps": [
      {
        "action": "action_type",
        "parameters": {
          "param1": "value1",
          "param2": "value2"
        },
        "description": "Human readable description",
        "dependencies": ["step_id_1", "step_id_2"],
        "conditional": {
          "condition": "if_condition",
          "true_action": "action_if_true",
          "false_action": "action_if_false"
        }
      }
    ]
  },
  "estimated_duration": 30,
  "requires_confirmation": false,
  "privacy_impact": "low",
  "explanation": "Brief explanation of the plan"
}
```

## Safety Guidelines

- Always prioritize user privacy and security
- Request confirmation for potentially destructive actions
- Respect app permissions and system limitations
- Provide clear explanations for complex actions
- Handle errors gracefully with fallback options

## User Request

{user_request}

Please analyze the request in the context of the current device state and create an appropriate task plan.
```

### 2. Scenario-Specific Prompts

#### Meeting Preparation Scenario

```
## Specialized Context: Meeting Preparation

You are helping the user prepare for an upcoming meeting. Focus on:
- Gathering relevant information from emails, documents, and calendar
- Summarizing key points and recent changes
- Checking logistics (location, traffic, dial-in info)
- Preparing briefing materials

**Meeting Details**: {meeting_details}
**Related Emails**: {email_context}
**Document Context**: {document_context}
**Traffic/Location**: {location_logistics}

Prioritize actions that help the user be well-prepared and on-time.
```

#### Content Creation Scenario

```
## Specialized Context: Content Creation

You are helping the user create content (emails, documents, presentations). Focus on:
- Gathering data from relevant apps and sources
- Analyzing and summarizing information
- Structuring content logically
- Suggesting improvements and next steps

**Content Type**: {content_type}
**Data Sources**: {available_data_sources}
**Target Audience**: {audience_context}
**Key Metrics/Data**: {metrics_context}

Prioritize accuracy, clarity, and actionable insights.
```

#### Routine Automation Scenario

```
## Specialized Context: Routine Automation

You are helping automate the user's routine activities. Focus on:
- Recognizing patterns in user behavior
- Anticipating needs based on time, location, and context
- Executing routine tasks proactively
- Adapting to user preferences and feedback

**Routine Type**: {routine_type}
**Historical Patterns**: {pattern_context}
**Current Deviation**: {deviation_context}
**User Preferences**: {preference_context}

Prioritize convenience while respecting user autonomy.
```

## JSON Schema Definitions

### 1. Task Plan Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "required": ["task_id", "confidence", "plan"],
  "properties": {
    "task_id": {
      "type": "string",
      "pattern": "^[a-zA-Z0-9_-]+$"
    },
    "confidence": {
      "type": "number",
      "minimum": 0,
      "maximum": 1
    },
    "plan": {
      "type": "object",
      "required": ["steps"],
      "properties": {
        "steps": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/ActionStep"
          }
        }
      }
    },
    "estimated_duration": {
      "type": "integer",
      "minimum": 0,
      "description": "Estimated duration in seconds"
    },
    "requires_confirmation": {
      "type": "boolean",
      "default": false
    },
    "privacy_impact": {
      "type": "string",
      "enum": ["low", "medium", "high"]
    },
    "explanation": {
      "type": "string",
      "maxLength": 500
    }
  },
  "definitions": {
    "ActionStep": {
      "type": "object",
      "required": ["action", "parameters"],
      "properties": {
        "action": {
          "type": "string",
          "enum": [
            "openApp", "setSystemSetting", "sendMessage", "makeCall",
            "sendEmail", "createCalendarEvent", "setAlarm", "searchWeb",
            "readFile", "writeFile", "analyzeContent", "waitForCondition"
          ]
        },
        "parameters": {
          "type": "object"
        },
        "description": {
          "type": "string"
        },
        "dependencies": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "conditional": {
          "type": "object",
          "properties": {
            "condition": {"type": "string"},
            "true_action": {"type": "string"},
            "false_action": {"type": "string"}
          }
        },
        "timeout": {
          "type": "integer",
          "minimum": 1,
          "default": 30
        }
      }
    }
  }
}
```

### 2. Context Snapshot Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "timestamp": {"type": "integer"},
    "current_app": {"type": "string"},
    "current_activity": {"type": "string"},
    "screen_state": {
      "type": "string",
      "enum": ["on", "off", "locked"]
    },
    "battery_level": {
      "type": "integer",
      "minimum": 0,
      "maximum": 100
    },
    "network_type": {
      "type": "string",
      "enum": ["wifi", "cellular", "none"]
    },
    "network_name": {"type": "string"},
    "location_context": {
      "type": "object",
      "properties": {
        "latitude": {"type": "number"},
        "longitude": {"type": "number"},
        "accuracy": {"type": "number"},
        "location_name": {"type": "string"}
      }
    },
    "time_context": {
      "type": "object",
      "properties": {
        "hour": {"type": "integer"},
        "day_of_week": {"type": "string"},
        "is_weekend": {"type": "boolean"}
      }
    },
    "notifications": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "package": {"type": "string"},
          "title": {"type": "string"},
          "text": {"type": "string"},
          "timestamp": {"type": "integer"}
        }
      }
    },
    "recent_apps": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "package": {"type": "string"},
          "last_used": {"type": "integer"},
          "usage_duration": {"type": "integer"}
        }
      }
    },
    "calendar_events": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "title": {"type": "string"},
          "start_time": {"type": "integer"},
          "end_time": {"type": "integer"},
          "location": {"type": "string"},
          "attendees": {"type": "array", "items": {"type": "string"}}
        }
      }
    }
  }
}
```

### 3. Available Actions Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "actions": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "action_type": {"type": "string"},
          "description": {"type": "string"},
          "parameters": {
            "type": "object",
            "properties": {
              "required": {
                "type": "array",
                "items": {"type": "string"}
              },
              "optional": {
                "type": "array",
                "items": {"type": "string"}
              },
              "schema": {"type": "object"}
            }
          },
          "permissions_required": {
            "type": "array",
            "items": {"type": "string"}
          },
          "requires_confirmation": {"type": "boolean"}
        }
      }
    }
  }
}
```

## Example API Interactions

### 1. Meeting Preparation Request

**Input Context**:
```json
{
  "user_request": "Prepare me for my 2 PM meeting with the marketing team",
  "current_time": "2024-01-15T13:30:00Z",
  "calendar_events": [
    {
      "title": "Marketing Team Sync",
      "start_time": "2024-01-15T14:00:00Z",
      "attendees": ["<EMAIL>", "<EMAIL>"]
    }
  ],
  "recent_emails": [
    {
      "from": "<EMAIL>",
      "subject": "Q4 Campaign Results",
      "timestamp": "2024-01-15T10:00:00Z"
    }
  ]
}
```

**Expected Response**:
```json
{
  "task_id": "meeting_prep_20240115_1400",
  "confidence": 0.92,
  "plan": {
    "steps": [
      {
        "action": "searchEmails",
        "parameters": {
          "query": "marketing team OR Q4 campaign",
          "time_range": "7_days",
          "max_results": 10
        },
        "description": "Find recent emails related to marketing team and campaigns"
      },
      {
        "action": "analyzeContent",
        "parameters": {
          "content_type": "email_summary",
          "focus": "key_decisions_and_metrics"
        },
        "description": "Summarize key points from recent marketing emails",
        "dependencies": ["step_1"]
      },
      {
        "action": "checkTraffic",
        "parameters": {
          "destination": "conference_room_b",
          "departure_time": "2024-01-15T13:55:00Z"
        },
        "description": "Check if travel time to meeting location"
      },
      {
        "action": "createNotification",
        "parameters": {
          "title": "Meeting Brief Ready",
          "text": "Marketing team meeting summary prepared",
          "action_button": "View Summary"
        },
        "description": "Notify user when briefing is ready",
        "dependencies": ["step_2"]
      }
    ]
  },
  "estimated_duration": 45,
  "requires_confirmation": false,
  "privacy_impact": "medium",
  "explanation": "I'll gather recent marketing emails, summarize key points, and check logistics for your meeting."
}
```
