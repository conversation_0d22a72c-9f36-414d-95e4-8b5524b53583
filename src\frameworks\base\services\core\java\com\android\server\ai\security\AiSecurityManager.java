/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.security;

import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Binder;
import android.os.Bundle;
import android.os.SystemClock;
import android.util.Log;
import android.util.Slog;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Central security manager for all AI services in Jarvis OS.
 * 
 * Handles permission validation, data classification, encryption,
 * audit logging, and security policy enforcement.
 */
public class AiSecurityManager {
    private static final String TAG = "AiSecurityManager";
    private static final boolean DEBUG = true;

    // Permission constants
    public static final String PERMISSION_AI_CONTEXT_ACCESS = "android.permission.AI_CONTEXT_ACCESS";
    public static final String PERMISSION_AI_PLANNING_EXECUTION = "android.permission.AI_PLANNING_EXECUTION";
    public static final String PERMISSION_AI_PERSONALIZATION = "android.permission.AI_PERSONALIZATION";
    
    // Context type permissions
    public static final String CONTEXT_APP_STATE = "ai.context.app_state";
    public static final String CONTEXT_NOTIFICATIONS = "ai.context.notifications";
    public static final String CONTEXT_LOCATION = "ai.context.location";
    public static final String CONTEXT_SENSORS = "ai.context.sensors";
    public static final String CONTEXT_COMMUNICATION = "ai.context.communication";
    public static final String CONTEXT_CALENDAR = "ai.context.calendar";
    public static final String CONTEXT_CONTACTS = "ai.context.contacts";

    // Data classification levels
    public static final int DATA_LEVEL_PUBLIC = 1;
    public static final int DATA_LEVEL_PERSONAL = 2;
    public static final int DATA_LEVEL_SENSITIVE = 3;
    public static final int DATA_LEVEL_CRITICAL = 4;

    private final Context mContext;
    private final PackageManager mPackageManager;
    private final AiAuditLogger mAuditLogger;
    private final AiEncryptionManager mEncryptionManager;
    private final SecureRandom mSecureRandom;
    
    // Permission cache for performance
    private final ConcurrentHashMap<String, PermissionCacheEntry> mPermissionCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> mLastPermissionCheck = new ConcurrentHashMap<>();
    
    // Security policies
    private final Map<String, SecurityPolicy> mSecurityPolicies = new HashMap<>();
    
    // Constants
    private static final long PERMISSION_CACHE_TIMEOUT_MS = 5 * 60 * 1000; // 5 minutes
    private static final int MAX_PERMISSION_REQUESTS_PER_HOUR = 100;

    public AiSecurityManager(Context context) {
        mContext = context;
        mPackageManager = context.getPackageManager();
        mAuditLogger = new AiAuditLogger(context);
        mEncryptionManager = new AiEncryptionManager(context);
        mSecureRandom = new SecureRandom();
        
        initializeSecurityPolicies();
        
        if (DEBUG) Slog.d(TAG, "AiSecurityManager initialized");
    }

    /**
     * Check if a package has permission for AI context access
     */
    public boolean hasContextPermission(String packageName, int uid) {
        return checkPermission(PERMISSION_AI_CONTEXT_ACCESS, packageName, uid);
    }

    /**
     * Check if a package has permission for AI planning execution
     */
    public boolean hasPlanningPermission(String packageName, int uid) {
        return checkPermission(PERMISSION_AI_PLANNING_EXECUTION, packageName, uid);
    }

    /**
     * Check if a package has permission for AI personalization
     */
    public boolean hasPersonalizationPermission(String packageName, int uid) {
        return checkPermission(PERMISSION_AI_PERSONALIZATION, packageName, uid);
    }

    /**
     * Check specific context type permission
     */
    public boolean checkContextPermission(String contextType, String packageName, int uid) {
        // First check base AI context permission
        if (!hasContextPermission(packageName, uid)) {
            mAuditLogger.logPermissionDenied(packageName, uid, contextType, "No base AI context permission");
            return false;
        }

        // Then check specific context type permission
        String specificPermission = getContextTypePermission(contextType);
        if (specificPermission != null) {
            boolean hasPermission = checkPermission(specificPermission, packageName, uid);
            if (!hasPermission) {
                mAuditLogger.logPermissionDenied(packageName, uid, contextType, "No specific context permission");
            }
            return hasPermission;
        }

        return true;
    }

    /**
     * Get available context types for a package
     */
    public List<String> getAvailableContextTypes(String packageName, int uid) {
        List<String> availableTypes = new ArrayList<>();
        
        if (!hasContextPermission(packageName, uid)) {
            return availableTypes; // Empty list
        }

        // Check each context type
        String[] contextTypes = {
            CONTEXT_APP_STATE, CONTEXT_NOTIFICATIONS, CONTEXT_LOCATION,
            CONTEXT_SENSORS, CONTEXT_COMMUNICATION, CONTEXT_CALENDAR, CONTEXT_CONTACTS
        };

        for (String contextType : contextTypes) {
            if (checkContextPermission(contextType, packageName, uid)) {
                availableTypes.add(contextType);
            }
        }

        return availableTypes;
    }

    /**
     * Classify data based on content and context
     */
    public int classifyData(Bundle data, String dataType) {
        if (data == null) {
            return DATA_LEVEL_PUBLIC;
        }

        // Check for critical data indicators
        if (containsCriticalData(data, dataType)) {
            return DATA_LEVEL_CRITICAL;
        }

        // Check for sensitive data indicators
        if (containsSensitiveData(data, dataType)) {
            return DATA_LEVEL_SENSITIVE;
        }

        // Check for personal data indicators
        if (containsPersonalData(data, dataType)) {
            return DATA_LEVEL_PERSONAL;
        }

        return DATA_LEVEL_PUBLIC;
    }

    /**
     * Encrypt data based on classification level
     */
    public Bundle encryptData(Bundle data, int classificationLevel) {
        return mEncryptionManager.encryptBundle(data, classificationLevel);
    }

    /**
     * Decrypt data
     */
    public Bundle decryptData(Bundle encryptedData, int classificationLevel) {
        return mEncryptionManager.decryptBundle(encryptedData, classificationLevel);
    }

    /**
     * Generate secure token for API requests
     */
    public String generateSecureToken(String packageName, String operation) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            String input = packageName + operation + System.currentTimeMillis() + mSecureRandom.nextLong();
            byte[] hash = digest.digest(input.getBytes());
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            String token = hexString.toString();
            mAuditLogger.logTokenGenerated(packageName, operation, token);
            return token;
        } catch (NoSuchAlgorithmException e) {
            Slog.e(TAG, "Error generating secure token", e);
            return null;
        }
    }

    /**
     * Validate security token
     */
    public boolean validateSecureToken(String token, String packageName, String operation) {
        // Implementation would validate token against stored tokens
        // For now, basic validation
        if (token == null || token.length() != 64) {
            mAuditLogger.logTokenValidationFailed(packageName, operation, "Invalid token format");
            return false;
        }
        
        mAuditLogger.logTokenValidated(packageName, operation, token);
        return true;
    }

    /**
     * Log security event
     */
    public void logSecurityEvent(String eventType, String packageName, int uid, Bundle details) {
        mAuditLogger.logSecurityEvent(eventType, packageName, uid, details);
    }

    // Private helper methods

    private boolean checkPermission(String permission, String packageName, int uid) {
        // Check cache first
        String cacheKey = packageName + ":" + permission;
        PermissionCacheEntry cached = mPermissionCache.get(cacheKey);
        long currentTime = SystemClock.elapsedRealtime();
        
        if (cached != null && (currentTime - cached.timestamp) < PERMISSION_CACHE_TIMEOUT_MS) {
            return cached.hasPermission;
        }

        // Check actual permission
        boolean hasPermission = mPackageManager.checkPermission(permission, packageName) 
                == PackageManager.PERMISSION_GRANTED;

        // Update cache
        mPermissionCache.put(cacheKey, new PermissionCacheEntry(hasPermission, currentTime));
        mLastPermissionCheck.put(cacheKey, currentTime);

        // Log permission check
        mAuditLogger.logPermissionCheck(packageName, uid, permission, hasPermission);

        return hasPermission;
    }

    private String getContextTypePermission(String contextType) {
        switch (contextType) {
            case CONTEXT_APP_STATE:
                return "android.permission.AI_CONTEXT_APP_STATE";
            case CONTEXT_NOTIFICATIONS:
                return "android.permission.AI_CONTEXT_NOTIFICATIONS";
            case CONTEXT_LOCATION:
                return "android.permission.AI_CONTEXT_LOCATION";
            case CONTEXT_SENSORS:
                return "android.permission.AI_CONTEXT_SENSORS";
            case CONTEXT_COMMUNICATION:
                return "android.permission.AI_CONTEXT_COMMUNICATION";
            case CONTEXT_CALENDAR:
                return "android.permission.AI_CONTEXT_CALENDAR";
            case CONTEXT_CONTACTS:
                return "android.permission.AI_CONTEXT_CONTACTS";
            default:
                return null;
        }
    }

    private boolean containsCriticalData(Bundle data, String dataType) {
        // Check for critical data patterns
        for (String key : data.keySet()) {
            String value = data.getString(key, "");
            if (key.toLowerCase().contains("password") ||
                key.toLowerCase().contains("pin") ||
                key.toLowerCase().contains("credential") ||
                key.toLowerCase().contains("token") ||
                value.matches(".*\\b\\d{4}\\s?\\d{4}\\s?\\d{4}\\s?\\d{4}\\b.*")) { // Credit card pattern
                return true;
            }
        }
        return false;
    }

    private boolean containsSensitiveData(Bundle data, String dataType) {
        // Check for sensitive data patterns
        for (String key : data.keySet()) {
            String value = data.getString(key, "");
            if (key.toLowerCase().contains("email") ||
                key.toLowerCase().contains("phone") ||
                key.toLowerCase().contains("address") ||
                key.toLowerCase().contains("location") ||
                value.matches(".*\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b.*")) { // Email pattern
                return true;
            }
        }
        return false;
    }

    private boolean containsPersonalData(Bundle data, String dataType) {
        // Check for personal data patterns
        return data.containsKey("name") ||
               data.containsKey("contact") ||
               data.containsKey("preference") ||
               dataType.contains("personal") ||
               dataType.contains("user");
    }

    private void initializeSecurityPolicies() {
        // Initialize default security policies
        mSecurityPolicies.put("context_collection", new SecurityPolicy(
            DATA_LEVEL_PERSONAL, true, 24 * 60 * 60 * 1000L)); // 24 hours retention
        mSecurityPolicies.put("task_execution", new SecurityPolicy(
            DATA_LEVEL_SENSITIVE, true, 7 * 24 * 60 * 60 * 1000L)); // 7 days retention
        mSecurityPolicies.put("personalization", new SecurityPolicy(
            DATA_LEVEL_PERSONAL, false, 365 * 24 * 60 * 60 * 1000L)); // 1 year retention
    }

    // Inner classes

    private static class PermissionCacheEntry {
        final boolean hasPermission;
        final long timestamp;

        PermissionCacheEntry(boolean hasPermission, long timestamp) {
            this.hasPermission = hasPermission;
            this.timestamp = timestamp;
        }
    }

    private static class SecurityPolicy {
        final int minDataLevel;
        final boolean requiresEncryption;
        final long retentionPeriodMs;

        SecurityPolicy(int minDataLevel, boolean requiresEncryption, long retentionPeriodMs) {
            this.minDataLevel = minDataLevel;
            this.requiresEncryption = requiresEncryption;
            this.retentionPeriodMs = retentionPeriodMs;
        }
    }
}
