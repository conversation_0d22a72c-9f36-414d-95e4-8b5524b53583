/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.security;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.util.Slog;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Audit logger for AI security events in Jarvis OS.
 * 
 * Provides comprehensive logging of security-related events including
 * permission checks, data access, token operations, and security violations.
 */
public class AiAuditLogger {
    private static final String TAG = "AiAuditLogger";
    private static final boolean DEBUG = true;

    // Log levels
    public static final int LOG_LEVEL_INFO = 1;
    public static final int LOG_LEVEL_WARNING = 2;
    public static final int LOG_LEVEL_ERROR = 3;
    public static final int LOG_LEVEL_CRITICAL = 4;

    // Event types
    public static final String EVENT_PERMISSION_CHECK = "PERMISSION_CHECK";
    public static final String EVENT_PERMISSION_DENIED = "PERMISSION_DENIED";
    public static final String EVENT_DATA_ACCESS = "DATA_ACCESS";
    public static final String EVENT_TOKEN_GENERATED = "TOKEN_GENERATED";
    public static final String EVENT_TOKEN_VALIDATED = "TOKEN_VALIDATED";
    public static final String EVENT_TOKEN_VALIDATION_FAILED = "TOKEN_VALIDATION_FAILED";
    public static final String EVENT_SECURITY_VIOLATION = "SECURITY_VIOLATION";
    public static final String EVENT_ENCRYPTION_OPERATION = "ENCRYPTION_OPERATION";
    public static final String EVENT_CONTEXT_COLLECTION = "CONTEXT_COLLECTION";
    public static final String EVENT_TASK_EXECUTION = "TASK_EXECUTION";

    private final Context mContext;
    private final ExecutorService mLogExecutor;
    private final SimpleDateFormat mDateFormat;
    private final File mLogDirectory;
    private final File mCurrentLogFile;

    public AiAuditLogger(Context context) {
        mContext = context;
        mLogExecutor = Executors.newSingleThreadExecutor();
        mDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.US);
        
        // Create log directory
        mLogDirectory = new File(context.getFilesDir(), "ai_audit_logs");
        if (!mLogDirectory.exists()) {
            mLogDirectory.mkdirs();
        }
        
        // Create current log file
        String logFileName = "ai_audit_" + 
            new SimpleDateFormat("yyyy_MM_dd", Locale.US).format(new Date()) + ".log";
        mCurrentLogFile = new File(mLogDirectory, logFileName);
        
        if (DEBUG) Slog.d(TAG, "AiAuditLogger initialized, log file: " + mCurrentLogFile.getPath());
    }

    /**
     * Log permission check event
     */
    public void logPermissionCheck(String packageName, int uid, String permission, boolean granted) {
        AuditEvent event = new AuditEvent.Builder()
            .setEventType(EVENT_PERMISSION_CHECK)
            .setLogLevel(LOG_LEVEL_INFO)
            .setPackageName(packageName)
            .setUid(uid)
            .addDetail("permission", permission)
            .addDetail("granted", String.valueOf(granted))
            .build();
        
        logEvent(event);
    }

    /**
     * Log permission denied event
     */
    public void logPermissionDenied(String packageName, int uid, String permission, String reason) {
        AuditEvent event = new AuditEvent.Builder()
            .setEventType(EVENT_PERMISSION_DENIED)
            .setLogLevel(LOG_LEVEL_WARNING)
            .setPackageName(packageName)
            .setUid(uid)
            .addDetail("permission", permission)
            .addDetail("reason", reason)
            .build();
        
        logEvent(event);
    }

    /**
     * Log data access event
     */
    public void logDataAccess(String packageName, int uid, String dataType, int dataLevel) {
        AuditEvent event = new AuditEvent.Builder()
            .setEventType(EVENT_DATA_ACCESS)
            .setLogLevel(LOG_LEVEL_INFO)
            .setPackageName(packageName)
            .setUid(uid)
            .addDetail("data_type", dataType)
            .addDetail("data_level", String.valueOf(dataLevel))
            .build();
        
        logEvent(event);
    }

    /**
     * Log token generation event
     */
    public void logTokenGenerated(String packageName, String operation, String tokenHash) {
        AuditEvent event = new AuditEvent.Builder()
            .setEventType(EVENT_TOKEN_GENERATED)
            .setLogLevel(LOG_LEVEL_INFO)
            .setPackageName(packageName)
            .addDetail("operation", operation)
            .addDetail("token_hash", hashToken(tokenHash))
            .build();
        
        logEvent(event);
    }

    /**
     * Log token validation event
     */
    public void logTokenValidated(String packageName, String operation, String tokenHash) {
        AuditEvent event = new AuditEvent.Builder()
            .setEventType(EVENT_TOKEN_VALIDATED)
            .setLogLevel(LOG_LEVEL_INFO)
            .setPackageName(packageName)
            .addDetail("operation", operation)
            .addDetail("token_hash", hashToken(tokenHash))
            .build();
        
        logEvent(event);
    }

    /**
     * Log token validation failure
     */
    public void logTokenValidationFailed(String packageName, String operation, String reason) {
        AuditEvent event = new AuditEvent.Builder()
            .setEventType(EVENT_TOKEN_VALIDATION_FAILED)
            .setLogLevel(LOG_LEVEL_ERROR)
            .setPackageName(packageName)
            .addDetail("operation", operation)
            .addDetail("reason", reason)
            .build();
        
        logEvent(event);
    }

    /**
     * Log security violation
     */
    public void logSecurityViolation(String packageName, int uid, String violationType, String details) {
        AuditEvent event = new AuditEvent.Builder()
            .setEventType(EVENT_SECURITY_VIOLATION)
            .setLogLevel(LOG_LEVEL_CRITICAL)
            .setPackageName(packageName)
            .setUid(uid)
            .addDetail("violation_type", violationType)
            .addDetail("details", details)
            .build();
        
        logEvent(event);
        
        // Also log to system log for immediate attention
        Slog.w(TAG, "SECURITY VIOLATION: " + violationType + " by " + packageName + " (uid:" + uid + ")");
    }

    /**
     * Log general security event
     */
    public void logSecurityEvent(String eventType, String packageName, int uid, Bundle details) {
        AuditEvent.Builder builder = new AuditEvent.Builder()
            .setEventType(eventType)
            .setLogLevel(LOG_LEVEL_INFO)
            .setPackageName(packageName)
            .setUid(uid);
        
        if (details != null) {
            for (String key : details.keySet()) {
                builder.addDetail(key, details.getString(key, ""));
            }
        }
        
        logEvent(builder.build());
    }

    /**
     * Log context collection event
     */
    public void logContextCollection(String contextType, int dataLevel, String source) {
        AuditEvent event = new AuditEvent.Builder()
            .setEventType(EVENT_CONTEXT_COLLECTION)
            .setLogLevel(LOG_LEVEL_INFO)
            .addDetail("context_type", contextType)
            .addDetail("data_level", String.valueOf(dataLevel))
            .addDetail("source", source)
            .build();
        
        logEvent(event);
    }

    /**
     * Log task execution event
     */
    public void logTaskExecution(String taskId, String packageName, String taskType, boolean success) {
        AuditEvent event = new AuditEvent.Builder()
            .setEventType(EVENT_TASK_EXECUTION)
            .setLogLevel(success ? LOG_LEVEL_INFO : LOG_LEVEL_WARNING)
            .setPackageName(packageName)
            .addDetail("task_id", taskId)
            .addDetail("task_type", taskType)
            .addDetail("success", String.valueOf(success))
            .build();
        
        logEvent(event);
    }

    /**
     * Get audit logs for a specific time range
     */
    public String getAuditLogs(long startTime, long endTime) {
        // Implementation would read from log files and filter by time range
        // For now, return a placeholder
        return "Audit logs from " + new Date(startTime) + " to " + new Date(endTime);
    }

    /**
     * Clear old audit logs
     */
    public void clearOldLogs(long retentionPeriodMs) {
        mLogExecutor.execute(() -> {
            long cutoffTime = System.currentTimeMillis() - retentionPeriodMs;
            File[] logFiles = mLogDirectory.listFiles();
            
            if (logFiles != null) {
                for (File logFile : logFiles) {
                    if (logFile.lastModified() < cutoffTime) {
                        if (logFile.delete()) {
                            if (DEBUG) Slog.d(TAG, "Deleted old log file: " + logFile.getName());
                        }
                    }
                }
            }
        });
    }

    // Private methods

    private void logEvent(AuditEvent event) {
        // Log to Android log
        String logMessage = formatLogMessage(event);
        switch (event.logLevel) {
            case LOG_LEVEL_INFO:
                if (DEBUG) Slog.i(TAG, logMessage);
                break;
            case LOG_LEVEL_WARNING:
                Slog.w(TAG, logMessage);
                break;
            case LOG_LEVEL_ERROR:
            case LOG_LEVEL_CRITICAL:
                Slog.e(TAG, logMessage);
                break;
        }
        
        // Write to audit log file asynchronously
        mLogExecutor.execute(() -> writeToLogFile(event));
    }

    private String formatLogMessage(AuditEvent event) {
        StringBuilder sb = new StringBuilder();
        sb.append("[").append(event.eventType).append("] ");
        if (event.packageName != null) {
            sb.append("pkg:").append(event.packageName).append(" ");
        }
        if (event.uid != -1) {
            sb.append("uid:").append(event.uid).append(" ");
        }
        
        for (String key : event.details.keySet()) {
            sb.append(key).append(":").append(event.details.get(key)).append(" ");
        }
        
        return sb.toString().trim();
    }

    private void writeToLogFile(AuditEvent event) {
        try (FileWriter writer = new FileWriter(mCurrentLogFile, true)) {
            String timestamp = mDateFormat.format(new Date(event.timestamp));
            String logLine = String.format("%s [%s] %s %s\n", 
                timestamp, 
                getLevelString(event.logLevel),
                event.eventType,
                formatEventDetails(event));
            
            writer.write(logLine);
            writer.flush();
        } catch (IOException e) {
            Slog.e(TAG, "Error writing to audit log file", e);
        }
    }

    private String formatEventDetails(AuditEvent event) {
        StringBuilder sb = new StringBuilder();
        if (event.packageName != null) {
            sb.append("pkg=").append(event.packageName).append(" ");
        }
        if (event.uid != -1) {
            sb.append("uid=").append(event.uid).append(" ");
        }
        
        for (String key : event.details.keySet()) {
            sb.append(key).append("=").append(event.details.get(key)).append(" ");
        }
        
        return sb.toString().trim();
    }

    private String getLevelString(int level) {
        switch (level) {
            case LOG_LEVEL_INFO: return "INFO";
            case LOG_LEVEL_WARNING: return "WARN";
            case LOG_LEVEL_ERROR: return "ERROR";
            case LOG_LEVEL_CRITICAL: return "CRITICAL";
            default: return "UNKNOWN";
        }
    }

    private String hashToken(String token) {
        // Return first 8 characters for logging (not the full token for security)
        return token != null && token.length() >= 8 ? token.substring(0, 8) + "..." : "invalid";
    }

    // Inner classes

    private static class AuditEvent {
        final long timestamp;
        final String eventType;
        final int logLevel;
        final String packageName;
        final int uid;
        final Bundle details;

        private AuditEvent(Builder builder) {
            this.timestamp = builder.timestamp;
            this.eventType = builder.eventType;
            this.logLevel = builder.logLevel;
            this.packageName = builder.packageName;
            this.uid = builder.uid;
            this.details = builder.details;
        }

        static class Builder {
            private long timestamp = System.currentTimeMillis();
            private String eventType;
            private int logLevel = LOG_LEVEL_INFO;
            private String packageName;
            private int uid = -1;
            private Bundle details = new Bundle();

            Builder setEventType(String eventType) {
                this.eventType = eventType;
                return this;
            }

            Builder setLogLevel(int logLevel) {
                this.logLevel = logLevel;
                return this;
            }

            Builder setPackageName(String packageName) {
                this.packageName = packageName;
                return this;
            }

            Builder setUid(int uid) {
                this.uid = uid;
                return this;
            }

            Builder addDetail(String key, String value) {
                this.details.putString(key, value);
                return this;
            }

            AuditEvent build() {
                return new AuditEvent(this);
            }
        }
    }
}
