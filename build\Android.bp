// Jarvis OS - Main Build Configuration
// This file defines the build targets for all Jarvis OS components

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

// =============================================================================
// AI Services Framework
// =============================================================================

// Core AI Services
java_library {
    name: "jarvis-ai-services",
    srcs: [
        "src/frameworks/base/services/core/java/com/android/server/ai/**/*.java",
    ],
    static_libs: [
        "jarvis-ai-interfaces",
        "jarvis-ai-security",
        "jarvis-native-libs",
    ],
    libs: [
        "framework",
        "services.core",
    ],
    platform_apis: true,
    certificate: "platform",
    privileged: true,
}

// AI AIDL Interfaces
java_library {
    name: "jarvis-ai-interfaces",
    srcs: [
        "src/frameworks/base/core/java/android/ai/**/*.java",
        "src/frameworks/base/core/java/android/ai/**/*.aidl",
    ],
    aidl: {
        local_include_dirs: ["src/frameworks/base/core/java"],
        include_dirs: [
            "frameworks/base/core/java",
        ],
    },
    libs: [
        "framework",
    ],
    platform_apis: true,
}

// AI Security Framework
java_library {
    name: "jarvis-ai-security",
    srcs: [
        "src/frameworks/base/services/core/java/com/android/server/ai/security/**/*.java",
    ],
    libs: [
        "framework",
        "services.core",
    ],
    static_libs: [
        "jarvis-ai-interfaces",
    ],
    platform_apis: true,
}

// =============================================================================
// Native Libraries
// =============================================================================

// AI Inference Library
cc_library_shared {
    name: "libai_inference",
    srcs: [
        "src/system/libai/src/inference/*.cpp",
        "src/system/libai/src/inference/*.c",
    ],
    header_libs: [
        "libai_headers",
    ],
    shared_libs: [
        "liblog",
        "libutils",
        "libcutils",
        "libbinder",
        "libhidlbase",
    ],
    cflags: [
        "-Wall",
        "-Werror",
        "-Wextra",
        "-O2",
        "-DJARVIS_AI_INFERENCE",
    ],
    include_dirs: [
        "src/system/libai/include",
    ],
    vendor_available: true,
    vndk: {
        enabled: true,
    },
}

// AI Security Library
cc_library_shared {
    name: "libai_security",
    srcs: [
        "src/system/libai/src/security/*.cpp",
        "src/system/libai/src/security/*.c",
    ],
    header_libs: [
        "libai_headers",
    ],
    shared_libs: [
        "liblog",
        "libutils",
        "libcutils",
        "libcrypto",
        "libssl",
        "libkeystore_binder",
    ],
    cflags: [
        "-Wall",
        "-Werror",
        "-Wextra",
        "-O2",
        "-DJARVIS_AI_SECURITY",
    ],
    include_dirs: [
        "src/system/libai/include",
    ],
    vendor_available: true,
    vndk: {
        enabled: true,
    },
}

// AI IPC Library
cc_library_shared {
    name: "libai_ipc",
    srcs: [
        "src/system/libai/src/ipc/*.cpp",
        "src/system/libai/src/ipc/*.c",
    ],
    header_libs: [
        "libai_headers",
    ],
    shared_libs: [
        "liblog",
        "libutils",
        "libcutils",
        "libbinder",
    ],
    cflags: [
        "-Wall",
        "-Werror",
        "-Wextra",
        "-O2",
        "-DJARVIS_AI_IPC",
    ],
    include_dirs: [
        "src/system/libai/include",
    ],
    vendor_available: true,
    vndk: {
        enabled: true,
    },
}

// AI Context Library
cc_library_shared {
    name: "libai_context",
    srcs: [
        "src/system/libai/src/context/*.cpp",
        "src/system/libai/src/context/*.c",
    ],
    header_libs: [
        "libai_headers",
    ],
    shared_libs: [
        "liblog",
        "libutils",
        "libcutils",
        "libai_security",
    ],
    cflags: [
        "-Wall",
        "-Werror",
        "-Wextra",
        "-O2",
        "-DJARVIS_AI_CONTEXT",
    ],
    include_dirs: [
        "src/system/libai/include",
    ],
    vendor_available: true,
    vndk: {
        enabled: true,
    },
}

// AI Headers
cc_library_headers {
    name: "libai_headers",
    export_include_dirs: [
        "src/system/libai/include",
    ],
    vendor_available: true,
    vndk: {
        enabled: true,
    },
}

// Combined Native Libraries
cc_library_shared {
    name: "jarvis-native-libs",
    whole_static_libs: [
        "libai_inference",
        "libai_security", 
        "libai_ipc",
        "libai_context",
    ],
    shared_libs: [
        "liblog",
        "libutils",
        "libcutils",
        "libbinder",
        "libcrypto",
        "libssl",
    ],
    vendor_available: true,
}

// =============================================================================
// JNI Bindings
// =============================================================================

// JNI for AI Services
cc_library_shared {
    name: "libjarvis_jni",
    srcs: [
        "src/frameworks/base/core/jni/android_server_ai_*.cpp",
    ],
    header_libs: [
        "jni_headers",
        "libai_headers",
    ],
    shared_libs: [
        "libandroid_runtime",
        "libnativehelper",
        "liblog",
        "libutils",
        "libcutils",
        "libbinder",
        "libai_inference",
        "libai_security",
        "libai_ipc",
        "libai_context",
    ],
    cflags: [
        "-Wall",
        "-Werror",
        "-Wno-unused-parameter",
        "-DJARVIS_JNI",
    ],
    include_dirs: [
        "src/system/libai/include",
    ],
}

// =============================================================================
// SystemUI Components
// =============================================================================

// Jarvis SystemUI Components
android_library {
    name: "jarvis-systemui",
    srcs: [
        "src/frameworks/base/packages/SystemUI/src/com/android/systemui/jarvis/**/*.java",
    ],
    resource_dirs: [
        "src/frameworks/base/packages/SystemUI/res-jarvis",
    ],
    static_libs: [
        "jarvis-ai-interfaces",
        "SystemUI-core",
    ],
    libs: [
        "framework",
        "framework-res",
    ],
    platform_apis: true,
    certificate: "platform",
    privileged: true,
}

// =============================================================================
// Settings Components
// =============================================================================

// Jarvis Settings Components
android_library {
    name: "jarvis-settings",
    srcs: [
        "src/packages/apps/Settings/src/com/android/settings/ai/**/*.java",
    ],
    resource_dirs: [
        "src/packages/apps/Settings/res-jarvis",
    ],
    static_libs: [
        "jarvis-ai-interfaces",
        "Settings-core",
    ],
    libs: [
        "framework",
        "framework-res",
    ],
    platform_apis: true,
}

// =============================================================================
// HAL Interfaces
// =============================================================================

// AI HAL Interface
hidl_interface {
    name: "android.hardware.ai",
    root: "android.hardware",
    vndk: {
        enabled: true,
    },
    srcs: [
        "src/hardware/interfaces/ai/1.0/*.hal",
    ],
    interfaces: [
        "android.hidl.base@1.0",
    ],
    gen_java: true,
}

// AI HAL Default Implementation
cc_binary {
    name: "android.hardware.ai@1.0-service",
    defaults: ["hidl_defaults"],
    relative_install_path: "hw",
    vendor: true,
    init_rc: ["src/hardware/interfaces/ai/1.0/default/<EMAIL>"],
    srcs: [
        "src/hardware/interfaces/ai/1.0/default/*.cpp",
    ],
    shared_libs: [
        "liblog",
        "libcutils",
        "libdl",
        "libbase",
        "libutils",
        "libhardware_legacy",
        "libhardware",
        "libhidlbase",
        "android.hardware.ai@1.0",
    ],
}

// =============================================================================
// Test Targets
// =============================================================================

// Unit Tests for AI Services
android_test {
    name: "JarvisAIServicesTests",
    srcs: [
        "tests/java/**/*.java",
    ],
    static_libs: [
        "jarvis-ai-services",
        "jarvis-ai-interfaces",
        "androidx.test.rules",
        "mockito-target-minus-junit4",
        "platform-test-annotations",
    ],
    libs: [
        "android.test.runner",
        "android.test.base",
        "android.test.mock",
    ],
    platform_apis: true,
    test_suites: ["device-tests"],
    certificate: "platform",
}

// Native Tests
cc_test {
    name: "libai_tests",
    srcs: [
        "tests/native/**/*.cpp",
    ],
    shared_libs: [
        "libai_inference",
        "libai_security",
        "libai_ipc",
        "libai_context",
        "liblog",
        "libutils",
        "libcutils",
    ],
    header_libs: [
        "libai_headers",
    ],
    test_suites: ["device-tests"],
}
