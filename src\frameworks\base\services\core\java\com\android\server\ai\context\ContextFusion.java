/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.context;

import android.ai.ContextSnapshot;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.util.Slog;

import com.android.server.ai.security.AiSecurityManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Context fusion engine for AI services in Jarvis OS.
 * 
 * Combines context information from multiple sources to create
 * comprehensive context snapshots with confidence scoring and
 * pattern recognition.
 */
public class ContextFusion {
    private static final String TAG = "ContextFusion";
    private static final boolean DEBUG = true;

    // Confidence thresholds
    private static final float MIN_CONFIDENCE_THRESHOLD = 0.3f;
    private static final float HIGH_CONFIDENCE_THRESHOLD = 0.8f;
    
    // Context weights for fusion
    private static final float WEIGHT_APP_STATE = 0.3f;
    private static final float WEIGHT_NOTIFICATIONS = 0.2f;
    private static final float WEIGHT_SENSORS = 0.2f;
    private static final float WEIGHT_LOCATION = 0.15f;
    private static final float WEIGHT_COMMUNICATION = 0.1f;
    private static final float WEIGHT_CALENDAR = 0.05f;

    private final Context mContext;
    private final AiSecurityManager mSecurityManager;
    
    // Context state tracking
    private final ConcurrentHashMap<String, ContextState> mContextStates = new ConcurrentHashMap<>();
    private final List<ContextPattern> mDetectedPatterns = new ArrayList<>();
    
    // Fusion statistics
    private long mTotalFusionOperations = 0;
    private long mHighConfidenceFusions = 0;
    private long mLowConfidenceFusions = 0;
    
    // Current fused context
    private ContextSnapshot mCurrentFusedContext;
    private float mCurrentConfidenceScore = 0.0f;

    public ContextFusion(Context context, AiSecurityManager securityManager) {
        mContext = context;
        mSecurityManager = securityManager;
        
        if (DEBUG) Slog.d(TAG, "ContextFusion initialized");
    }

    /**
     * Process context update from collectors
     */
    public void processContextUpdate(Bundle contextData) {
        if (contextData == null) {
            return;
        }

        try {
            // Update individual context states
            updateContextStates(contextData);
            
            // Perform context fusion
            ContextSnapshot fusedContext = fuseContextData(contextData);
            
            // Calculate confidence score
            float confidenceScore = calculateConfidenceScore(fusedContext, contextData);
            
            // Update current context if confidence is sufficient
            if (confidenceScore >= MIN_CONFIDENCE_THRESHOLD) {
                mCurrentFusedContext = fusedContext;
                mCurrentConfidenceScore = confidenceScore;
                
                // Detect patterns
                detectPatterns(fusedContext);
                
                // Update statistics
                mTotalFusionOperations++;
                if (confidenceScore >= HIGH_CONFIDENCE_THRESHOLD) {
                    mHighConfidenceFusions++;
                } else {
                    mLowConfidenceFusions++;
                }
                
                if (DEBUG) Slog.d(TAG, "Context fused with confidence: " + confidenceScore);
            } else {
                if (DEBUG) Slog.w(TAG, "Context fusion confidence too low: " + confidenceScore);
            }
            
        } catch (Exception e) {
            Slog.e(TAG, "Error processing context update", e);
        }
    }

    /**
     * Generate current context snapshot
     */
    public ContextSnapshot generateContextSnapshot() {
        if (mCurrentFusedContext == null) {
            // Create basic context snapshot
            ContextSnapshot context = new ContextSnapshot();
            context.timestamp = System.currentTimeMillis();
            context.privacyLevel = AiSecurityManager.DATA_LEVEL_PUBLIC;
            return context;
        }
        
        // Return copy of current fused context
        ContextSnapshot snapshot = new ContextSnapshot();
        snapshot.timestamp = mCurrentFusedContext.timestamp;
        snapshot.currentApp = mCurrentFusedContext.currentApp;
        snapshot.currentActivity = mCurrentFusedContext.currentActivity;
        snapshot.appStates = mCurrentFusedContext.appStates;
        snapshot.sensorData = mCurrentFusedContext.sensorData;
        snapshot.notificationData = mCurrentFusedContext.notificationData;
        snapshot.userInteractionData = mCurrentFusedContext.userInteractionData;
        snapshot.environmentalData = mCurrentFusedContext.environmentalData;
        snapshot.calendarData = mCurrentFusedContext.calendarData;
        snapshot.communicationData = mCurrentFusedContext.communicationData;
        snapshot.privacyLevel = mCurrentFusedContext.privacyLevel;
        
        return snapshot;
    }

    /**
     * Get current confidence score
     */
    public float getCurrentConfidenceScore() {
        return mCurrentConfidenceScore;
    }

    /**
     * Get detected patterns
     */
    public List<ContextPattern> getDetectedPatterns() {
        return new ArrayList<>(mDetectedPatterns);
    }

    /**
     * Get fusion statistics
     */
    public Bundle getFusionStatistics() {
        Bundle stats = new Bundle();
        stats.putLong("total_fusions", mTotalFusionOperations);
        stats.putLong("high_confidence_fusions", mHighConfidenceFusions);
        stats.putLong("low_confidence_fusions", mLowConfidenceFusions);
        stats.putFloat("current_confidence", mCurrentConfidenceScore);
        stats.putInt("detected_patterns", mDetectedPatterns.size());
        stats.putInt("tracked_states", mContextStates.size());
        
        return stats;
    }

    // Private methods

    private void updateContextStates(Bundle contextData) {
        long timestamp = contextData.getLong("timestamp", System.currentTimeMillis());
        
        // Update app state
        Bundle appState = contextData.getBundle("app_state");
        if (appState != null) {
            updateContextState("app_state", appState, timestamp);
        }
        
        // Update notification state
        Bundle notifications = contextData.getBundle("notifications");
        if (notifications != null) {
            updateContextState("notifications", notifications, timestamp);
        }
        
        // Update sensor state
        Bundle sensors = contextData.getBundle("sensors");
        if (sensors != null) {
            updateContextState("sensors", sensors, timestamp);
        }
        
        // Update location state
        Bundle location = contextData.getBundle("location");
        if (location != null) {
            updateContextState("location", location, timestamp);
        }
        
        // Update communication state
        Bundle communication = contextData.getBundle("communication");
        if (communication != null) {
            updateContextState("communication", communication, timestamp);
        }
        
        // Update calendar state
        Bundle calendar = contextData.getBundle("calendar");
        if (calendar != null) {
            updateContextState("calendar", calendar, timestamp);
        }
    }

    private void updateContextState(String contextType, Bundle data, long timestamp) {
        ContextState state = mContextStates.get(contextType);
        if (state == null) {
            state = new ContextState(contextType);
            mContextStates.put(contextType, state);
        }
        
        state.updateData(data, timestamp);
    }

    private ContextSnapshot fuseContextData(Bundle contextData) {
        ContextSnapshot snapshot = new ContextSnapshot();
        snapshot.timestamp = contextData.getLong("timestamp", System.currentTimeMillis());
        
        // Fuse app state data
        Bundle appState = contextData.getBundle("app_state");
        if (appState != null) {
            snapshot.currentApp = appState.getString("current_app");
            snapshot.currentActivity = appState.getString("current_activity");
            snapshot.appStates = appState;
        }
        
        // Fuse sensor data
        Bundle sensors = contextData.getBundle("sensors");
        if (sensors != null) {
            snapshot.sensorData = sensors;
        }
        
        // Fuse notification data
        Bundle notifications = contextData.getBundle("notifications");
        if (notifications != null) {
            snapshot.notificationData = notifications;
        }
        
        // Fuse location data
        Bundle location = contextData.getBundle("location");
        if (location != null) {
            snapshot.environmentalData = location;
        }
        
        // Fuse communication data
        Bundle communication = contextData.getBundle("communication");
        if (communication != null) {
            snapshot.communicationData = communication;
        }
        
        // Fuse calendar data
        Bundle calendar = contextData.getBundle("calendar");
        if (calendar != null) {
            snapshot.calendarData = calendar;
        }
        
        // Determine privacy level based on fused data
        snapshot.privacyLevel = determinePrivacyLevel(snapshot);
        
        return snapshot;
    }

    private float calculateConfidenceScore(ContextSnapshot context, Bundle rawData) {
        float totalScore = 0.0f;
        float totalWeight = 0.0f;
        
        // Score app state confidence
        if (context.appStates != null) {
            float appConfidence = calculateAppStateConfidence(context.appStates);
            totalScore += appConfidence * WEIGHT_APP_STATE;
            totalWeight += WEIGHT_APP_STATE;
        }
        
        // Score notification confidence
        if (context.notificationData != null) {
            float notificationConfidence = calculateNotificationConfidence(context.notificationData);
            totalScore += notificationConfidence * WEIGHT_NOTIFICATIONS;
            totalWeight += WEIGHT_NOTIFICATIONS;
        }
        
        // Score sensor confidence
        if (context.sensorData != null) {
            float sensorConfidence = calculateSensorConfidence(context.sensorData);
            totalScore += sensorConfidence * WEIGHT_SENSORS;
            totalWeight += WEIGHT_SENSORS;
        }
        
        // Score location confidence
        if (context.environmentalData != null) {
            float locationConfidence = calculateLocationConfidence(context.environmentalData);
            totalScore += locationConfidence * WEIGHT_LOCATION;
            totalWeight += WEIGHT_LOCATION;
        }
        
        // Score communication confidence
        if (context.communicationData != null) {
            float commConfidence = calculateCommunicationConfidence(context.communicationData);
            totalScore += commConfidence * WEIGHT_COMMUNICATION;
            totalWeight += WEIGHT_COMMUNICATION;
        }
        
        // Score calendar confidence
        if (context.calendarData != null) {
            float calendarConfidence = calculateCalendarConfidence(context.calendarData);
            totalScore += calendarConfidence * WEIGHT_CALENDAR;
            totalWeight += WEIGHT_CALENDAR;
        }
        
        return totalWeight > 0 ? totalScore / totalWeight : 0.0f;
    }

    private float calculateAppStateConfidence(Bundle appState) {
        // High confidence if we have current app and activity
        if (appState.getString("current_app") != null && 
            appState.getString("current_activity") != null) {
            return 0.9f;
        } else if (appState.getString("current_app") != null) {
            return 0.7f;
        }
        return 0.3f;
    }

    private float calculateNotificationConfidence(Bundle notifications) {
        // Confidence based on number and recency of notifications
        int notificationCount = notifications.getInt("count", 0);
        long lastNotification = notifications.getLong("last_notification", 0);
        long timeSinceLastNotification = System.currentTimeMillis() - lastNotification;
        
        if (notificationCount > 0 && timeSinceLastNotification < 60000) { // 1 minute
            return 0.8f;
        } else if (notificationCount > 0) {
            return 0.5f;
        }
        return 0.2f;
    }

    private float calculateSensorConfidence(Bundle sensors) {
        // Confidence based on sensor data availability and freshness
        boolean hasAccelerometer = sensors.containsKey("accelerometer");
        boolean hasLocation = sensors.containsKey("location");
        boolean hasAmbientLight = sensors.containsKey("ambient_light");
        
        int sensorCount = (hasAccelerometer ? 1 : 0) + (hasLocation ? 1 : 0) + (hasAmbientLight ? 1 : 0);
        return Math.min(0.9f, sensorCount * 0.3f);
    }

    private float calculateLocationConfidence(Bundle location) {
        // Confidence based on location accuracy and recency
        float accuracy = location.getFloat("accuracy", Float.MAX_VALUE);
        long timestamp = location.getLong("timestamp", 0);
        long age = System.currentTimeMillis() - timestamp;
        
        if (accuracy < 100 && age < 300000) { // 100m accuracy, 5 minutes old
            return 0.9f;
        } else if (accuracy < 1000 && age < 600000) { // 1km accuracy, 10 minutes old
            return 0.6f;
        }
        return 0.3f;
    }

    private float calculateCommunicationConfidence(Bundle communication) {
        // Confidence based on recent communication activity
        long lastCall = communication.getLong("last_call", 0);
        long lastMessage = communication.getLong("last_message", 0);
        long recentActivity = Math.max(lastCall, lastMessage);
        long timeSinceActivity = System.currentTimeMillis() - recentActivity;
        
        if (timeSinceActivity < 300000) { // 5 minutes
            return 0.8f;
        } else if (timeSinceActivity < 3600000) { // 1 hour
            return 0.5f;
        }
        return 0.2f;
    }

    private float calculateCalendarConfidence(Bundle calendar) {
        // Confidence based on upcoming events
        int upcomingEvents = calendar.getInt("upcoming_events", 0);
        long nextEventTime = calendar.getLong("next_event_time", 0);
        long timeToNextEvent = nextEventTime - System.currentTimeMillis();
        
        if (upcomingEvents > 0 && timeToNextEvent < 3600000) { // 1 hour
            return 0.9f;
        } else if (upcomingEvents > 0) {
            return 0.6f;
        }
        return 0.3f;
    }

    private int determinePrivacyLevel(ContextSnapshot context) {
        int maxLevel = AiSecurityManager.DATA_LEVEL_PUBLIC;
        
        // Check each data component for privacy level
        if (context.communicationData != null) {
            maxLevel = Math.max(maxLevel, AiSecurityManager.DATA_LEVEL_SENSITIVE);
        }
        
        if (context.environmentalData != null) {
            maxLevel = Math.max(maxLevel, AiSecurityManager.DATA_LEVEL_PERSONAL);
        }
        
        if (context.calendarData != null) {
            maxLevel = Math.max(maxLevel, AiSecurityManager.DATA_LEVEL_PERSONAL);
        }
        
        return maxLevel;
    }

    private void detectPatterns(ContextSnapshot context) {
        // Simple pattern detection - can be enhanced with ML
        // For now, detect basic patterns like "work hours", "commute", etc.
        
        // TODO: Implement pattern detection algorithms
        // This would analyze historical context to identify user routines
    }

    // Inner classes

    private static class ContextState {
        final String contextType;
        Bundle currentData;
        long lastUpdate;
        List<Bundle> history;
        
        ContextState(String contextType) {
            this.contextType = contextType;
            this.history = new ArrayList<>();
        }
        
        void updateData(Bundle data, long timestamp) {
            this.currentData = data;
            this.lastUpdate = timestamp;
            
            // Keep limited history
            history.add(data);
            if (history.size() > 10) {
                history.remove(0);
            }
        }
    }

    public static class ContextPattern {
        public final String patternType;
        public final String description;
        public final float confidence;
        public final long detectedAt;
        
        public ContextPattern(String patternType, String description, float confidence) {
            this.patternType = patternType;
            this.description = description;
            this.confidence = confidence;
            this.detectedAt = System.currentTimeMillis();
        }
    }
}
