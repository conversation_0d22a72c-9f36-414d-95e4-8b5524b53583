#!/bin/bash

# Jarvis OS - AOSP Development Environment Setup Script
# This script sets up the development environment for Jarvis OS

set -e

echo "🚀 Setting up Jarvis OS Development Environment..."

# Configuration
AOSP_VERSION="android-14.0.0_r50"  # Android 14 stable
JARVIS_ROOT=$(pwd)
AOSP_ROOT="${JARVIS_ROOT}/aosp"
BUILD_TOOLS_VERSION="34.0.0"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check system requirements
check_system_requirements() {
    print_status "Checking system requirements..."
    
    # Check OS
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_success "Linux detected"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        print_success "macOS detected"
    else
        print_error "Unsupported OS: $OSTYPE"
        exit 1
    fi
    
    # Check disk space (need at least 400GB for AOSP)
    available_space=$(df -BG . | tail -1 | awk '{print $4}' | sed 's/G//')
    if [ "$available_space" -lt 400 ]; then
        print_warning "Low disk space: ${available_space}GB available. AOSP requires ~400GB"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        print_success "Sufficient disk space: ${available_space}GB"
    fi
    
    # Check RAM (recommend 16GB+)
    total_ram=$(free -g | awk '/^Mem:/{print $2}' 2>/dev/null || sysctl -n hw.memsize | awk '{print int($1/1024/1024/1024)}' 2>/dev/null || echo "unknown")
    if [ "$total_ram" != "unknown" ] && [ "$total_ram" -lt 16 ]; then
        print_warning "Low RAM: ${total_ram}GB. Recommended: 16GB+"
    else
        print_success "RAM: ${total_ram}GB"
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Ubuntu/Debian dependencies
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y \
                git-core gnupg flex bison build-essential zip curl \
                zlib1g-dev gcc-multilib g++-multilib libc6-dev-i386 \
                libncurses5 lib32ncurses5-dev x11proto-core-dev libx11-dev \
                lib32z1-dev libgl1-mesa-dev libxml2-utils xsltproc unzip \
                fontconfig python3 python3-pip openjdk-11-jdk
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS dependencies
        if ! command -v brew &> /dev/null; then
            print_error "Homebrew not found. Please install Homebrew first."
            exit 1
        fi
        brew install git gnupg coreutils findutils gnu-tar gnu-sed gawk gnutls gnu-indent gnu-getopt gradle
    fi
    
    print_success "Dependencies installed"
}

# Setup repo tool
setup_repo() {
    print_status "Setting up repo tool..."
    
    mkdir -p ~/bin
    curl https://storage.googleapis.com/git-repo-downloads/repo > ~/bin/repo
    chmod a+x ~/bin/repo
    
    # Add to PATH if not already there
    if [[ ":$PATH:" != *":$HOME/bin:"* ]]; then
        echo 'export PATH="$HOME/bin:$PATH"' >> ~/.bashrc
        export PATH="$HOME/bin:$PATH"
    fi
    
    print_success "Repo tool installed"
}

# Initialize AOSP
init_aosp() {
    print_status "Initializing AOSP repository..."
    
    mkdir -p "$AOSP_ROOT"
    cd "$AOSP_ROOT"
    
    # Configure git
    git config --global user.name "Jarvis OS Team"
    git config --global user.email "<EMAIL>"
    
    # Initialize repo
    repo init -u https://android.googlesource.com/platform/manifest -b "$AOSP_VERSION"
    
    print_success "AOSP repository initialized"
}

# Download AOSP source (this will take a while)
download_aosp() {
    print_status "Downloading AOSP source code..."
    print_warning "This will take several hours depending on your internet connection..."
    
    cd "$AOSP_ROOT"
    repo sync -c -j$(nproc)
    
    print_success "AOSP source code downloaded"
}

# Create Jarvis OS directory structure
create_jarvis_structure() {
    print_status "Creating Jarvis OS directory structure..."
    
    cd "$AOSP_ROOT"
    
    # Create AI service directories
    mkdir -p frameworks/base/services/core/java/com/android/server/ai
    mkdir -p frameworks/base/services/core/java/com/android/server/ai/context
    mkdir -p frameworks/base/services/core/java/com/android/server/ai/planning
    mkdir -p frameworks/base/services/core/java/com/android/server/ai/personalization
    mkdir -p frameworks/base/services/core/java/com/android/server/ai/security
    mkdir -p frameworks/base/services/core/java/com/android/server/ai/gemini
    mkdir -p frameworks/base/services/core/java/com/android/server/ai/execution
    
    # Create AIDL interface directories
    mkdir -p frameworks/base/core/java/android/ai
    
    # Create native library directories
    mkdir -p system/libai
    mkdir -p system/libai/include
    mkdir -p system/libai/src
    
    # Create HAL directories
    mkdir -p hardware/interfaces/ai/1.0
    mkdir -p hardware/interfaces/ai/1.0/default
    
    # Create SystemUI Jarvis components
    mkdir -p frameworks/base/packages/SystemUI/src/com/android/systemui/jarvis
    
    # Create Settings AI components
    mkdir -p packages/apps/Settings/src/com/android/settings/ai
    
    # Create SELinux policy directories
    mkdir -p system/sepolicy/private/jarvis
    mkdir -p system/sepolicy/public/jarvis
    
    print_success "Jarvis OS directory structure created"
}

# Copy Jarvis OS source files
copy_jarvis_sources() {
    print_status "Copying Jarvis OS source files..."
    
    # Copy our skeleton implementations
    if [ -d "${JARVIS_ROOT}/src" ]; then
        cp -r "${JARVIS_ROOT}/src/"* "$AOSP_ROOT/"
        print_success "Source files copied"
    else
        print_warning "No source files found in ${JARVIS_ROOT}/src"
    fi
}

# Setup build environment
setup_build_environment() {
    print_status "Setting up build environment..."
    
    cd "$AOSP_ROOT"
    source build/envsetup.sh
    
    # Choose target (we'll use aosp_arm64 for emulator testing)
    lunch aosp_arm64-eng
    
    print_success "Build environment configured"
}

# Create development scripts
create_dev_scripts() {
    print_status "Creating development scripts..."
    
    # Build script
    cat > "${JARVIS_ROOT}/build-jarvis.sh" << 'EOF'
#!/bin/bash
cd aosp
source build/envsetup.sh
lunch aosp_arm64-eng
make -j$(nproc) systemimage
EOF
    chmod +x "${JARVIS_ROOT}/build-jarvis.sh"
    
    # Test script
    cat > "${JARVIS_ROOT}/test-jarvis.sh" << 'EOF'
#!/bin/bash
cd aosp
source build/envsetup.sh
lunch aosp_arm64-eng
emulator -avd jarvis_test -no-snapshot-load
EOF
    chmod +x "${JARVIS_ROOT}/test-jarvis.sh"
    
    # Clean script
    cat > "${JARVIS_ROOT}/clean-jarvis.sh" << 'EOF'
#!/bin/bash
cd aosp
make clean
EOF
    chmod +x "${JARVIS_ROOT}/clean-jarvis.sh"
    
    print_success "Development scripts created"
}

# Main execution
main() {
    echo "🤖 Jarvis OS - AOSP Development Environment Setup"
    echo "=============================================="
    
    check_system_requirements
    install_dependencies
    setup_repo
    
    if [ ! -d "$AOSP_ROOT/.repo" ]; then
        init_aosp
        download_aosp
    else
        print_status "AOSP already initialized, syncing..."
        cd "$AOSP_ROOT"
        repo sync -c -j$(nproc)
    fi
    
    create_jarvis_structure
    copy_jarvis_sources
    setup_build_environment
    create_dev_scripts
    
    echo
    print_success "🎉 Jarvis OS development environment setup complete!"
    echo
    echo "Next steps:"
    echo "1. cd aosp && source build/envsetup.sh"
    echo "2. lunch aosp_arm64-eng"
    echo "3. make -j$(nproc) systemimage"
    echo
    echo "Or use the convenience scripts:"
    echo "- ./build-jarvis.sh  # Build the system"
    echo "- ./test-jarvis.sh   # Run in emulator"
    echo "- ./clean-jarvis.sh  # Clean build"
}

# Run main function
main "$@"
