/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.context;

import android.ai.ContextSnapshot;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.os.Bundle;
import android.util.Log;
import android.util.Slog;

import com.android.server.ai.security.AiSecurityManager;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Database for storing AI context information in Jarvis OS.
 * 
 * Provides secure storage and retrieval of context snapshots with
 * automatic cleanup and privacy controls.
 */
public class ContextDatabase {
    private static final String TAG = "ContextDatabase";
    private static final boolean DEBUG = true;

    // Database configuration
    private static final String DATABASE_NAME = "jarvis_context.db";
    private static final int DATABASE_VERSION = 1;
    
    // Table names
    private static final String TABLE_CONTEXT_SNAPSHOTS = "context_snapshots";
    private static final String TABLE_CONTEXT_METADATA = "context_metadata";
    
    // Context snapshots table columns
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_TIMESTAMP = "timestamp";
    private static final String COLUMN_CONTEXT_TYPE = "context_type";
    private static final String COLUMN_DATA_LEVEL = "data_level";
    private static final String COLUMN_ENCRYPTED_DATA = "encrypted_data";
    private static final String COLUMN_PACKAGE_SOURCE = "package_source";
    private static final String COLUMN_RETENTION_POLICY = "retention_policy";
    
    // Metadata table columns
    private static final String COLUMN_KEY = "key";
    private static final String COLUMN_VALUE = "value";
    private static final String COLUMN_UPDATED = "updated";

    // Retention policies (in milliseconds)
    private static final long RETENTION_CONTEXT_DEFAULT = 30L * 24 * 60 * 60 * 1000; // 30 days
    private static final long RETENTION_SENSITIVE_DATA = 7L * 24 * 60 * 60 * 1000; // 7 days
    private static final long RETENTION_PERSONAL_DATA = 90L * 24 * 60 * 60 * 1000; // 90 days

    private final Context mContext;
    private final AiSecurityManager mSecurityManager;
    private final DatabaseHelper mDatabaseHelper;
    private final ExecutorService mDatabaseExecutor;
    
    // Statistics
    private long mTotalContextsStored = 0;
    private long mTotalContextsRetrieved = 0;
    private long mLastCleanupTime = 0;

    public ContextDatabase(Context context) {
        mContext = context;
        mSecurityManager = new AiSecurityManager(context);
        mDatabaseHelper = new DatabaseHelper(context);
        mDatabaseExecutor = Executors.newSingleThreadExecutor();
        
        // Schedule periodic cleanup
        schedulePeriodicCleanup();
        
        if (DEBUG) Slog.d(TAG, "ContextDatabase initialized");
    }

    /**
     * Store a context snapshot
     */
    public void storeContext(ContextSnapshot context) {
        if (context == null) {
            return;
        }

        mDatabaseExecutor.execute(() -> {
            try {
                SQLiteDatabase db = mDatabaseHelper.getWritableDatabase();
                
                // Classify and encrypt the context data
                Bundle contextData = contextSnapshotToBundle(context);
                int dataLevel = mSecurityManager.classifyData(contextData, "context");
                Bundle encryptedData = mSecurityManager.encryptData(contextData, dataLevel);
                
                // Determine retention policy based on data level
                long retentionPeriod = getRetentionPeriod(dataLevel);
                
                // Insert into database
                String sql = "INSERT INTO " + TABLE_CONTEXT_SNAPSHOTS + " (" +
                    COLUMN_TIMESTAMP + ", " +
                    COLUMN_CONTEXT_TYPE + ", " +
                    COLUMN_DATA_LEVEL + ", " +
                    COLUMN_ENCRYPTED_DATA + ", " +
                    COLUMN_PACKAGE_SOURCE + ", " +
                    COLUMN_RETENTION_POLICY + ") VALUES (?, ?, ?, ?, ?, ?)";
                
                db.execSQL(sql, new Object[] {
                    context.timestamp,
                    "general", // TODO: Determine context type from snapshot
                    dataLevel,
                    bundleToString(encryptedData),
                    "system", // TODO: Get actual package source
                    retentionPeriod
                });
                
                mTotalContextsStored++;
                
                if (DEBUG) Slog.d(TAG, "Context stored with data level: " + dataLevel);
                
            } catch (Exception e) {
                Slog.e(TAG, "Error storing context", e);
            }
        });
    }

    /**
     * Get historical context data
     */
    public List<ContextSnapshot> getHistoricalContext(long startTime, long endTime, String packageName) {
        List<ContextSnapshot> contexts = new ArrayList<>();
        
        try {
            SQLiteDatabase db = mDatabaseHelper.getReadableDatabase();
            
            String sql = "SELECT * FROM " + TABLE_CONTEXT_SNAPSHOTS + 
                " WHERE " + COLUMN_TIMESTAMP + " >= ? AND " + COLUMN_TIMESTAMP + " <= ?" +
                " ORDER BY " + COLUMN_TIMESTAMP + " DESC LIMIT 1000";
            
            Cursor cursor = db.rawQuery(sql, new String[] {
                String.valueOf(startTime),
                String.valueOf(endTime)
            });
            
            while (cursor.moveToNext()) {
                try {
                    long timestamp = cursor.getLong(cursor.getColumnIndex(COLUMN_TIMESTAMP));
                    int dataLevel = cursor.getInt(cursor.getColumnIndex(COLUMN_DATA_LEVEL));
                    String encryptedDataStr = cursor.getString(cursor.getColumnIndex(COLUMN_ENCRYPTED_DATA));
                    
                    // Decrypt the data
                    Bundle encryptedData = stringToBundle(encryptedDataStr);
                    Bundle decryptedData = mSecurityManager.decryptData(encryptedData, dataLevel);
                    
                    if (decryptedData != null) {
                        ContextSnapshot context = bundleToContextSnapshot(decryptedData);
                        contexts.add(context);
                    }
                    
                } catch (Exception e) {
                    Slog.w(TAG, "Error decrypting context entry", e);
                }
            }
            
            cursor.close();
            mTotalContextsRetrieved += contexts.size();
            
            if (DEBUG) Slog.d(TAG, "Retrieved " + contexts.size() + " historical contexts");
            
        } catch (Exception e) {
            Slog.e(TAG, "Error retrieving historical context", e);
        }
        
        return contexts;
    }

    /**
     * Get recent context snapshots
     */
    public List<ContextSnapshot> getRecentContext(int limit) {
        long endTime = System.currentTimeMillis();
        long startTime = endTime - (24 * 60 * 60 * 1000); // Last 24 hours
        
        return getHistoricalContext(startTime, endTime, "system");
    }

    /**
     * Delete old context data based on retention policies
     */
    public void cleanupOldData() {
        mDatabaseExecutor.execute(() -> {
            try {
                SQLiteDatabase db = mDatabaseHelper.getWritableDatabase();
                long currentTime = System.currentTimeMillis();
                
                // Delete expired contexts based on retention policy
                String sql = "DELETE FROM " + TABLE_CONTEXT_SNAPSHOTS + 
                    " WHERE (" + COLUMN_TIMESTAMP + " + " + COLUMN_RETENTION_POLICY + ") < ?";
                
                db.execSQL(sql, new Object[] { currentTime });
                
                // Update cleanup time
                mLastCleanupTime = currentTime;
                
                if (DEBUG) Slog.d(TAG, "Cleanup completed");
                
            } catch (Exception e) {
                Slog.e(TAG, "Error during cleanup", e);
            }
        });
    }

    /**
     * Get database statistics
     */
    public Bundle getStatistics() {
        Bundle stats = new Bundle();
        
        try {
            SQLiteDatabase db = mDatabaseHelper.getReadableDatabase();
            
            // Count total contexts
            Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM " + TABLE_CONTEXT_SNAPSHOTS, null);
            if (cursor.moveToFirst()) {
                stats.putLong("total_contexts", cursor.getLong(0));
            }
            cursor.close();
            
            // Count by data level
            cursor = db.rawQuery("SELECT " + COLUMN_DATA_LEVEL + ", COUNT(*) FROM " + 
                TABLE_CONTEXT_SNAPSHOTS + " GROUP BY " + COLUMN_DATA_LEVEL, null);
            while (cursor.moveToNext()) {
                int level = cursor.getInt(0);
                long count = cursor.getLong(1);
                stats.putLong("level_" + level + "_count", count);
            }
            cursor.close();
            
        } catch (Exception e) {
            Slog.e(TAG, "Error getting statistics", e);
        }
        
        stats.putLong("total_stored", mTotalContextsStored);
        stats.putLong("total_retrieved", mTotalContextsRetrieved);
        stats.putLong("last_cleanup", mLastCleanupTime);
        
        return stats;
    }

    /**
     * Dump database state for debugging
     */
    public void dump(PrintWriter pw) {
        pw.println("    ContextDatabase State:");
        
        Bundle stats = getStatistics();
        pw.println("      Total Contexts: " + stats.getLong("total_contexts", 0));
        pw.println("      Total Stored: " + mTotalContextsStored);
        pw.println("      Total Retrieved: " + mTotalContextsRetrieved);
        pw.println("      Last Cleanup: " + mLastCleanupTime);
        
        // Data level breakdown
        for (int level = 1; level <= 4; level++) {
            long count = stats.getLong("level_" + level + "_count", 0);
            if (count > 0) {
                pw.println("      Level " + level + " Contexts: " + count);
            }
        }
    }

    // Private methods

    private long getRetentionPeriod(int dataLevel) {
        switch (dataLevel) {
            case AiSecurityManager.DATA_LEVEL_CRITICAL:
            case AiSecurityManager.DATA_LEVEL_SENSITIVE:
                return RETENTION_SENSITIVE_DATA;
            case AiSecurityManager.DATA_LEVEL_PERSONAL:
                return RETENTION_PERSONAL_DATA;
            default:
                return RETENTION_CONTEXT_DEFAULT;
        }
    }

    private void schedulePeriodicCleanup() {
        // Schedule cleanup every 24 hours
        mDatabaseExecutor.execute(() -> {
            while (true) {
                try {
                    Thread.sleep(24 * 60 * 60 * 1000); // 24 hours
                    cleanupOldData();
                } catch (InterruptedException e) {
                    break;
                }
            }
        });
    }

    private Bundle contextSnapshotToBundle(ContextSnapshot context) {
        Bundle bundle = new Bundle();
        bundle.putLong("timestamp", context.timestamp);
        bundle.putString("currentApp", context.currentApp);
        bundle.putString("currentActivity", context.currentActivity);
        // Add other context fields as needed
        return bundle;
    }

    private ContextSnapshot bundleToContextSnapshot(Bundle bundle) {
        ContextSnapshot context = new ContextSnapshot();
        context.timestamp = bundle.getLong("timestamp", 0);
        context.currentApp = bundle.getString("currentApp");
        context.currentActivity = bundle.getString("currentActivity");
        // Restore other context fields as needed
        return context;
    }

    private String bundleToString(Bundle bundle) {
        // Simple serialization - in production, use more robust method
        return bundle.toString();
    }

    private Bundle stringToBundle(String str) {
        // Simple deserialization - in production, use more robust method
        Bundle bundle = new Bundle();
        // Parse the string back to bundle
        return bundle;
    }

    // Database helper class
    private static class DatabaseHelper extends SQLiteOpenHelper {
        
        public DatabaseHelper(Context context) {
            super(context, DATABASE_NAME, null, DATABASE_VERSION);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            // Create context snapshots table
            String createContextTable = "CREATE TABLE " + TABLE_CONTEXT_SNAPSHOTS + " (" +
                COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                COLUMN_TIMESTAMP + " INTEGER NOT NULL, " +
                COLUMN_CONTEXT_TYPE + " TEXT NOT NULL, " +
                COLUMN_DATA_LEVEL + " INTEGER NOT NULL, " +
                COLUMN_ENCRYPTED_DATA + " TEXT NOT NULL, " +
                COLUMN_PACKAGE_SOURCE + " TEXT, " +
                COLUMN_RETENTION_POLICY + " INTEGER NOT NULL" +
                ")";
            db.execSQL(createContextTable);

            // Create metadata table
            String createMetadataTable = "CREATE TABLE " + TABLE_CONTEXT_METADATA + " (" +
                COLUMN_KEY + " TEXT PRIMARY KEY, " +
                COLUMN_VALUE + " TEXT, " +
                COLUMN_UPDATED + " INTEGER" +
                ")";
            db.execSQL(createMetadataTable);

            // Create indexes for performance
            db.execSQL("CREATE INDEX idx_timestamp ON " + TABLE_CONTEXT_SNAPSHOTS + "(" + COLUMN_TIMESTAMP + ")");
            db.execSQL("CREATE INDEX idx_context_type ON " + TABLE_CONTEXT_SNAPSHOTS + "(" + COLUMN_CONTEXT_TYPE + ")");
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            // Handle database upgrades
            if (oldVersion < newVersion) {
                db.execSQL("DROP TABLE IF EXISTS " + TABLE_CONTEXT_SNAPSHOTS);
                db.execSQL("DROP TABLE IF EXISTS " + TABLE_CONTEXT_METADATA);
                onCreate(db);
            }
        }
    }
}
