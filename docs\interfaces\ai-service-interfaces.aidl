// AIDL Interface Definitions for Jarvis OS AI Services

// =============================================================================
// AiContextEngineService Interface
// =============================================================================

// frameworks/base/core/java/android/ai/IAiContextEngine.aidl
interface IAiContextEngine {
    /**
     * Get current system-wide context snapshot
     */
    ContextSnapshot getCurrentContext();
    
    /**
     * Register a listener for context changes
     */
    void registerContextListener(in IContextListener listener);
    
    /**
     * Unregister a context listener
     */
    void unregisterContextListener(in IContextListener listener);
    
    /**
     * Request permission to access specific context types
     */
    boolean requestContextPermission(String contextType, String callingPackage);
    
    /**
     * Get available context types for the calling package
     */
    List<String> getAvailableContextTypes();
    
    /**
     * Get historical context data (with time range)
     */
    List<ContextSnapshot> getHistoricalContext(long startTime, long endTime);
    
    /**
     * Enable/disable specific context collection
     */
    void setContextCollectionEnabled(String contextType, boolean enabled);
}

// frameworks/base/core/java/android/ai/IContextListener.aidl
interface IContextListener {
    /**
     * Called when context changes
     */
    void onContextChanged(in ContextSnapshot newContext);
    
    /**
     * Called when specific context type updates
     */
    void onContextTypeUpdated(String contextType, in Bundle contextData);
}

// =============================================================================
// AiPlanningOrchestrationService Interface
// =============================================================================

// frameworks/base/core/java/android/ai/IAiPlanningOrchestration.aidl
interface IAiPlanningOrchestration {
    /**
     * Plan a task from natural language goal
     */
    PlanResult planTask(String naturalLanguageGoal, in ContextSnapshot context);
    
    /**
     * Execute a planned task
     */
    ExecutionResult executeTask(in TaskPlan plan);
    
    /**
     * Execute a task with real-time planning
     */
    ExecutionResult executeTaskWithPlanning(String goal, in ContextSnapshot context);
    
    /**
     * Get execution status of a running task
     */
    ExecutionStatus getExecutionStatus(String taskId);
    
    /**
     * Cancel a running task
     */
    boolean cancelTask(String taskId);
    
    /**
     * Register an action provider for specific action types
     */
    void registerActionProvider(String actionType, in IActionProvider provider);
    
    /**
     * Unregister an action provider
     */
    void unregisterActionProvider(String actionType);
    
    /**
     * Get available actions for current context
     */
    List<ActionCapability> getAvailableActions(in ContextSnapshot context);
    
    /**
     * Validate a task plan before execution
     */
    ValidationResult validateTaskPlan(in TaskPlan plan);
}

// frameworks/base/core/java/android/ai/IActionProvider.aidl
interface IActionProvider {
    /**
     * Execute a specific action
     */
    ActionResult executeAction(in ActionRequest request);
    
    /**
     * Validate if action can be executed
     */
    boolean canExecuteAction(in ActionRequest request);
    
    /**
     * Get action capabilities
     */
    ActionCapability getCapability();
}

// =============================================================================
// AiPersonalizationService Interface
// =============================================================================

// frameworks/base/core/java/android/ai/IAiPersonalization.aidl
interface IAiPersonalization {
    /**
     * Get user profile for personalization
     */
    UserProfile getUserProfile();
    
    /**
     * Update a specific preference
     */
    void updatePreference(String key, in Bundle value);
    
    /**
     * Get personalized model for specific use case
     */
    LearningModel getPersonalizedModel(String modelType);
    
    /**
     * Record user interaction for learning
     */
    void recordUserInteraction(in UserInteraction interaction);
    
    /**
     * Get personalized recommendations
     */
    List<Recommendation> getRecommendations(String category, in ContextSnapshot context);
    
    /**
     * Update learning model with feedback
     */
    void provideFeedback(String modelType, in FeedbackData feedback);
    
    /**
     * Reset personalization data
     */
    void resetPersonalizationData(String dataType);
    
    /**
     * Export personalization data
     */
    Bundle exportPersonalizationData();
    
    /**
     * Import personalization data
     */
    boolean importPersonalizationData(in Bundle data);
}

// =============================================================================
// Data Structure Definitions (Parcelable classes)
// =============================================================================

// frameworks/base/core/java/android/ai/ContextSnapshot.aidl
parcelable ContextSnapshot {
    long timestamp;
    String currentApp;
    String currentActivity;
    Bundle appStates;
    Bundle sensorData;
    Bundle notificationData;
    Bundle userInteractionData;
    Bundle environmentalData;
    Bundle calendarData;
    Bundle communicationData;
    int privacyLevel;
}

// frameworks/base/core/java/android/ai/TaskPlan.aidl
parcelable TaskPlan {
    String taskId;
    String originalGoal;
    List<ActionStep> actionSteps;
    Bundle dependencies;
    Bundle conditionalLogic;
    long estimatedDuration;
    int confidenceScore;
    Bundle metadata;
}

// frameworks/base/core/java/android/ai/ActionStep.aidl
parcelable ActionStep {
    String actionId;
    String actionType;
    Bundle parameters;
    List<String> dependencies;
    Bundle conditionalExecution;
    int priority;
    long timeout;
}

// frameworks/base/core/java/android/ai/PlanResult.aidl
parcelable PlanResult {
    boolean success;
    TaskPlan plan;
    String errorMessage;
    int confidenceScore;
    List<String> warnings;
    Bundle alternatives;
}

// frameworks/base/core/java/android/ai/ExecutionResult.aidl
parcelable ExecutionResult {
    String taskId;
    boolean success;
    String errorMessage;
    Bundle results;
    List<ActionResult> actionResults;
    long executionTime;
    Bundle metadata;
}

// frameworks/base/core/java/android/ai/ExecutionStatus.aidl
parcelable ExecutionStatus {
    String taskId;
    int status; // PENDING, RUNNING, COMPLETED, FAILED, CANCELLED
    int progress; // 0-100
    String currentAction;
    Bundle statusData;
    long startTime;
    long estimatedCompletion;
}

// frameworks/base/core/java/android/ai/ActionRequest.aidl
parcelable ActionRequest {
    String actionType;
    Bundle parameters;
    ContextSnapshot context;
    String requestId;
    int priority;
    long timeout;
}

// frameworks/base/core/java/android/ai/ActionResult.aidl
parcelable ActionResult {
    String actionId;
    boolean success;
    String errorMessage;
    Bundle resultData;
    long executionTime;
    Bundle metadata;
}

// frameworks/base/core/java/android/ai/ActionCapability.aidl
parcelable ActionCapability {
    String actionType;
    String description;
    List<String> requiredPermissions;
    Bundle parameterSchema;
    Bundle constraints;
    boolean requiresUserConfirmation;
}

// frameworks/base/core/java/android/ai/UserProfile.aidl
parcelable UserProfile {
    String userId;
    Bundle preferences;
    Bundle learnedPatterns;
    Bundle communicationStyle;
    Bundle privacySettings;
    Bundle automationPreferences;
    long lastUpdated;
}

// frameworks/base/core/java/android/ai/UserInteraction.aidl
parcelable UserInteraction {
    String interactionType;
    Bundle interactionData;
    ContextSnapshot context;
    long timestamp;
    String outcome;
    Bundle feedback;
}

// frameworks/base/core/java/android/ai/LearningModel.aidl
parcelable LearningModel {
    String modelType;
    String modelVersion;
    Bundle modelData;
    Bundle metadata;
    long lastTrained;
    int accuracy;
}

// frameworks/base/core/java/android/ai/Recommendation.aidl
parcelable Recommendation {
    String recommendationType;
    String title;
    String description;
    Bundle actionData;
    int confidence;
    long expirationTime;
    Bundle metadata;
}

// frameworks/base/core/java/android/ai/FeedbackData.aidl
parcelable FeedbackData {
    String feedbackType;
    Bundle feedbackData;
    int rating; // 1-5 scale
    String comments;
    long timestamp;
}

// frameworks/base/core/java/android/ai/ValidationResult.aidl
parcelable ValidationResult {
    boolean isValid;
    List<String> errors;
    List<String> warnings;
    Bundle suggestions;
    int securityRisk; // LOW, MEDIUM, HIGH
}
