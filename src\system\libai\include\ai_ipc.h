/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef JARVIS_AI_IPC_H
#define JARVIS_AI_IPC_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Jarvis OS AI IPC Library
 * 
 * Provides high-performance inter-process communication for AI services
 * with support for shared memory, message queues, and event notifications.
 */

// Version information
#define AI_IPC_VERSION_MAJOR 1
#define AI_IPC_VERSION_MINOR 0
#define AI_IPC_VERSION_PATCH 0

// Return codes
typedef enum {
    AI_IPC_SUCCESS = 0,
    AI_IPC_ERROR_INVALID_PARAM = -1,
    AI_IPC_ERROR_OUT_OF_MEMORY = -2,
    AI_IPC_ERROR_CONNECTION_FAILED = -3,
    AI_IPC_ERROR_TIMEOUT = -4,
    AI_IPC_ERROR_PERMISSION_DENIED = -5,
    AI_IPC_ERROR_BUFFER_FULL = -6,
    AI_IPC_ERROR_NOT_CONNECTED = -7,
    AI_IPC_ERROR_INVALID_MESSAGE = -8
} ai_ipc_result_t;

// Message types
typedef enum {
    AI_IPC_MSG_CONTEXT_UPDATE = 0,
    AI_IPC_MSG_TASK_REQUEST = 1,
    AI_IPC_MSG_TASK_RESPONSE = 2,
    AI_IPC_MSG_PERSONALIZATION_UPDATE = 3,
    AI_IPC_MSG_SECURITY_EVENT = 4,
    AI_IPC_MSG_PERFORMANCE_METRICS = 5,
    AI_IPC_MSG_CUSTOM = 100
} ai_ipc_message_type_t;

// Message priorities
typedef enum {
    AI_IPC_PRIORITY_LOW = 0,
    AI_IPC_PRIORITY_NORMAL = 1,
    AI_IPC_PRIORITY_HIGH = 2,
    AI_IPC_PRIORITY_CRITICAL = 3
} ai_ipc_priority_t;

// Connection types
typedef enum {
    AI_IPC_CONNECTION_BINDER = 0,     // Android Binder IPC
    AI_IPC_CONNECTION_SOCKET = 1,     // Unix domain sockets
    AI_IPC_CONNECTION_SHARED_MEM = 2, // Shared memory
    AI_IPC_CONNECTION_PIPE = 3        // Named pipes
} ai_ipc_connection_type_t;

// Forward declarations
typedef struct ai_ipc_connection ai_ipc_connection_t;
typedef struct ai_ipc_message ai_ipc_message_t;
typedef struct ai_ipc_shared_buffer ai_ipc_shared_buffer_t;
typedef struct ai_ipc_event ai_ipc_event_t;

// Message structure
struct ai_ipc_message {
    uint32_t message_id;              // Unique message identifier
    ai_ipc_message_type_t type;       // Message type
    ai_ipc_priority_t priority;       // Message priority
    uint32_t sender_pid;              // Sender process ID
    uint32_t sender_uid;              // Sender user ID
    uint64_t timestamp;               // Message timestamp
    size_t payload_size;              // Size of payload data
    void* payload;                    // Message payload data
    bool requires_response;           // Whether message requires response
    uint32_t correlation_id;          // For request-response correlation
};

// Connection configuration
typedef struct {
    ai_ipc_connection_type_t type;    // Connection type
    const char* endpoint_name;        // Endpoint name/path
    size_t max_message_size;          // Maximum message size
    size_t queue_size;                // Message queue size
    uint32_t timeout_ms;              // Operation timeout in milliseconds
    bool enable_encryption;           // Enable message encryption
    bool enable_compression;          // Enable message compression
    const char* security_context;     // Security context for permissions
} ai_ipc_config_t;

// Shared buffer configuration
typedef struct {
    size_t buffer_size;               // Size of shared buffer
    bool read_only;                   // Whether buffer is read-only
    bool enable_synchronization;      // Enable synchronization primitives
    const char* name;                 // Buffer name for identification
} ai_ipc_shared_buffer_config_t;

// Event configuration
typedef struct {
    const char* event_name;           // Event name
    bool auto_reset;                  // Whether event auto-resets
    bool manual_reset;                // Whether event requires manual reset
    uint32_t timeout_ms;              // Wait timeout in milliseconds
} ai_ipc_event_config_t;

// Performance metrics
typedef struct {
    uint64_t messages_sent;           // Total messages sent
    uint64_t messages_received;       // Total messages received
    uint64_t bytes_sent;              // Total bytes sent
    uint64_t bytes_received;          // Total bytes received
    float avg_latency_ms;             // Average message latency
    float max_latency_ms;             // Maximum message latency
    uint32_t queue_depth;             // Current queue depth
    uint32_t dropped_messages;        // Number of dropped messages
} ai_ipc_metrics_t;

// Message callback function type
typedef void (*ai_ipc_message_callback_t)(const ai_ipc_message_t* message, void* user_data);

// Event callback function type
typedef void (*ai_ipc_event_callback_t)(const ai_ipc_event_t* event, void* user_data);

/**
 * Initialize the AI IPC library
 * 
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_init(void);

/**
 * Cleanup and shutdown the AI IPC library
 */
void ai_ipc_cleanup(void);

/**
 * Get library version information
 * 
 * @param major Pointer to store major version
 * @param minor Pointer to store minor version
 * @param patch Pointer to store patch version
 */
void ai_ipc_get_version(int* major, int* minor, int* patch);

/**
 * Create an IPC connection
 * 
 * @param config Connection configuration
 * @param connection Pointer to store the created connection
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_connection_create(const ai_ipc_config_t* config,
                                        ai_ipc_connection_t** connection);

/**
 * Connect to a remote endpoint
 * 
 * @param connection The IPC connection
 * @param endpoint_name Name of the endpoint to connect to
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_connection_connect(ai_ipc_connection_t* connection,
                                         const char* endpoint_name);

/**
 * Start listening for incoming connections
 * 
 * @param connection The IPC connection
 * @param callback Callback function for incoming messages
 * @param user_data User data to pass to callback
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_connection_listen(ai_ipc_connection_t* connection,
                                        ai_ipc_message_callback_t callback,
                                        void* user_data);

/**
 * Disconnect and destroy an IPC connection
 * 
 * @param connection The connection to destroy
 */
void ai_ipc_connection_destroy(ai_ipc_connection_t* connection);

/**
 * Send a message through the IPC connection
 * 
 * @param connection The IPC connection
 * @param message The message to send
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_send_message(ai_ipc_connection_t* connection,
                                   const ai_ipc_message_t* message);

/**
 * Send a message and wait for response
 * 
 * @param connection The IPC connection
 * @param request The request message to send
 * @param response Pointer to store the response message
 * @param timeout_ms Timeout in milliseconds
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_send_request(ai_ipc_connection_t* connection,
                                   const ai_ipc_message_t* request,
                                   ai_ipc_message_t** response,
                                   uint32_t timeout_ms);

/**
 * Receive a message from the IPC connection
 * 
 * @param connection The IPC connection
 * @param message Pointer to store the received message
 * @param timeout_ms Timeout in milliseconds
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_receive_message(ai_ipc_connection_t* connection,
                                      ai_ipc_message_t** message,
                                      uint32_t timeout_ms);

/**
 * Create a message
 * 
 * @param type Message type
 * @param priority Message priority
 * @param payload Message payload data
 * @param payload_size Size of payload data
 * @param message Pointer to store the created message
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_message_create(ai_ipc_message_type_t type,
                                     ai_ipc_priority_t priority,
                                     const void* payload,
                                     size_t payload_size,
                                     ai_ipc_message_t** message);

/**
 * Destroy a message and free its resources
 * 
 * @param message The message to destroy
 */
void ai_ipc_message_destroy(ai_ipc_message_t* message);

/**
 * Create a shared memory buffer
 * 
 * @param config Shared buffer configuration
 * @param buffer Pointer to store the created buffer
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_shared_buffer_create(const ai_ipc_shared_buffer_config_t* config,
                                           ai_ipc_shared_buffer_t** buffer);

/**
 * Map a shared memory buffer into the process address space
 * 
 * @param buffer The shared buffer to map
 * @param data Pointer to store the mapped data address
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_shared_buffer_map(ai_ipc_shared_buffer_t* buffer,
                                        void** data);

/**
 * Unmap and destroy a shared memory buffer
 * 
 * @param buffer The shared buffer to destroy
 */
void ai_ipc_shared_buffer_destroy(ai_ipc_shared_buffer_t* buffer);

/**
 * Create an IPC event for synchronization
 * 
 * @param config Event configuration
 * @param event Pointer to store the created event
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_event_create(const ai_ipc_event_config_t* config,
                                   ai_ipc_event_t** event);

/**
 * Signal an IPC event
 * 
 * @param event The event to signal
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_event_signal(ai_ipc_event_t* event);

/**
 * Wait for an IPC event to be signaled
 * 
 * @param event The event to wait for
 * @param timeout_ms Timeout in milliseconds
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_event_wait(ai_ipc_event_t* event, uint32_t timeout_ms);

/**
 * Reset an IPC event
 * 
 * @param event The event to reset
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_event_reset(ai_ipc_event_t* event);

/**
 * Destroy an IPC event
 * 
 * @param event The event to destroy
 */
void ai_ipc_event_destroy(ai_ipc_event_t* event);

/**
 * Get performance metrics for an IPC connection
 * 
 * @param connection The IPC connection
 * @param metrics Pointer to store the metrics
 * @return AI_IPC_SUCCESS on success, error code otherwise
 */
ai_ipc_result_t ai_ipc_get_metrics(ai_ipc_connection_t* connection,
                                  ai_ipc_metrics_t* metrics);

/**
 * Get error message for a result code
 * 
 * @param result The result code
 * @return Human-readable error message
 */
const char* ai_ipc_get_error_message(ai_ipc_result_t result);

/**
 * Enable or disable debug logging
 * 
 * @param enable true to enable debug logging, false to disable
 */
void ai_ipc_set_debug_logging(bool enable);

#ifdef __cplusplus
}
#endif

#endif // JARVIS_AI_IPC_H
