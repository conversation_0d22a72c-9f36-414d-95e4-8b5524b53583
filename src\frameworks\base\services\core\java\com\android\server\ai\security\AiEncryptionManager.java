/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.security;

import android.content.Context;
import android.os.Bundle;
import android.security.keystore.KeyGenParameterSpec;
import android.security.keystore.KeyProperties;
import android.util.Base64;
import android.util.Log;
import android.util.Slog;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * Encryption manager for AI data in Jarvis OS.
 * 
 * Provides encryption and decryption services for different data classification levels
 * using Android Keystore and secure enclaves where available.
 */
public class AiEncryptionManager {
    private static final String TAG = "AiEncryptionManager";
    private static final boolean DEBUG = true;

    // Encryption algorithms
    private static final String AES_TRANSFORMATION = "AES/GCM/NoPadding";
    private static final String KEY_ALGORITHM = "AES";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 16;

    // Keystore aliases for different data levels
    private static final String KEY_ALIAS_PUBLIC = "jarvis_ai_public_key";
    private static final String KEY_ALIAS_PERSONAL = "jarvis_ai_personal_key";
    private static final String KEY_ALIAS_SENSITIVE = "jarvis_ai_sensitive_key";
    private static final String KEY_ALIAS_CRITICAL = "jarvis_ai_critical_key";

    private final Context mContext;
    private final KeyStore mKeyStore;
    private final SecureRandom mSecureRandom;
    private final Map<Integer, String> mKeyAliases;

    public AiEncryptionManager(Context context) {
        mContext = context;
        mSecureRandom = new SecureRandom();
        mKeyAliases = new HashMap<>();
        
        // Initialize key aliases mapping
        mKeyAliases.put(AiSecurityManager.DATA_LEVEL_PUBLIC, KEY_ALIAS_PUBLIC);
        mKeyAliases.put(AiSecurityManager.DATA_LEVEL_PERSONAL, KEY_ALIAS_PERSONAL);
        mKeyAliases.put(AiSecurityManager.DATA_LEVEL_SENSITIVE, KEY_ALIAS_SENSITIVE);
        mKeyAliases.put(AiSecurityManager.DATA_LEVEL_CRITICAL, KEY_ALIAS_CRITICAL);

        try {
            mKeyStore = KeyStore.getInstance("AndroidKeyStore");
            mKeyStore.load(null);
            initializeKeys();
            if (DEBUG) Slog.d(TAG, "AiEncryptionManager initialized");
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize AiEncryptionManager", e);
        }
    }

    /**
     * Encrypt a Bundle based on data classification level
     */
    public Bundle encryptBundle(Bundle data, int classificationLevel) {
        if (data == null) {
            return null;
        }

        try {
            // Serialize bundle to byte array
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            oos.writeObject(bundleToMap(data));
            oos.close();
            byte[] serializedData = baos.toByteArray();

            // Encrypt the serialized data
            byte[] encryptedData = encryptBytes(serializedData, classificationLevel);

            // Create result bundle with encrypted data
            Bundle result = new Bundle();
            result.putString("encrypted_data", Base64.encodeToString(encryptedData, Base64.DEFAULT));
            result.putInt("classification_level", classificationLevel);
            result.putLong("encryption_timestamp", System.currentTimeMillis());
            
            if (DEBUG) Slog.d(TAG, "Bundle encrypted with level: " + classificationLevel);
            return result;
        } catch (Exception e) {
            Slog.e(TAG, "Error encrypting bundle", e);
            return data; // Return original data if encryption fails
        }
    }

    /**
     * Decrypt a Bundle
     */
    public Bundle decryptBundle(Bundle encryptedData, int classificationLevel) {
        if (encryptedData == null || !encryptedData.containsKey("encrypted_data")) {
            return encryptedData; // Return as-is if not encrypted
        }

        try {
            String encodedData = encryptedData.getString("encrypted_data");
            byte[] encryptedBytes = Base64.decode(encodedData, Base64.DEFAULT);

            // Decrypt the data
            byte[] decryptedBytes = decryptBytes(encryptedBytes, classificationLevel);

            // Deserialize back to bundle
            ByteArrayInputStream bais = new ByteArrayInputStream(decryptedBytes);
            ObjectInputStream ois = new ObjectInputStream(bais);
            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = (Map<String, Object>) ois.readObject();
            ois.close();

            Bundle result = mapToBundle(dataMap);
            if (DEBUG) Slog.d(TAG, "Bundle decrypted with level: " + classificationLevel);
            return result;
        } catch (Exception e) {
            Slog.e(TAG, "Error decrypting bundle", e);
            return null;
        }
    }

    /**
     * Encrypt string data
     */
    public String encryptString(String data, int classificationLevel) {
        if (data == null) {
            return null;
        }

        try {
            byte[] encryptedBytes = encryptBytes(data.getBytes("UTF-8"), classificationLevel);
            return Base64.encodeToString(encryptedBytes, Base64.DEFAULT);
        } catch (Exception e) {
            Slog.e(TAG, "Error encrypting string", e);
            return data; // Return original if encryption fails
        }
    }

    /**
     * Decrypt string data
     */
    public String decryptString(String encryptedData, int classificationLevel) {
        if (encryptedData == null) {
            return null;
        }

        try {
            byte[] encryptedBytes = Base64.decode(encryptedData, Base64.DEFAULT);
            byte[] decryptedBytes = decryptBytes(encryptedBytes, classificationLevel);
            return new String(decryptedBytes, "UTF-8");
        } catch (Exception e) {
            Slog.e(TAG, "Error decrypting string", e);
            return null;
        }
    }

    /**
     * Check if data is encrypted
     */
    public boolean isEncrypted(Bundle data) {
        return data != null && data.containsKey("encrypted_data") && 
               data.containsKey("classification_level");
    }

    /**
     * Get encryption key for data level (for testing purposes)
     */
    public boolean hasKeyForLevel(int classificationLevel) {
        String keyAlias = mKeyAliases.get(classificationLevel);
        if (keyAlias == null) {
            return false;
        }

        try {
            return mKeyStore.containsAlias(keyAlias);
        } catch (Exception e) {
            Slog.e(TAG, "Error checking key existence", e);
            return false;
        }
    }

    // Private methods

    private void initializeKeys() throws Exception {
        // Initialize keys for each data classification level
        for (Map.Entry<Integer, String> entry : mKeyAliases.entrySet()) {
            int level = entry.getKey();
            String alias = entry.getValue();
            
            if (!mKeyStore.containsAlias(alias)) {
                generateKey(alias, level);
                if (DEBUG) Slog.d(TAG, "Generated encryption key for level: " + level);
            }
        }
    }

    private void generateKey(String keyAlias, int classificationLevel) throws Exception {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(KEY_ALGORITHM, "AndroidKeyStore");
        
        KeyGenParameterSpec.Builder builder = new KeyGenParameterSpec.Builder(
                keyAlias, KeyProperties.PURPOSE_ENCRYPT | KeyProperties.PURPOSE_DECRYPT)
                .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE);

        // Set security requirements based on classification level
        switch (classificationLevel) {
            case AiSecurityManager.DATA_LEVEL_CRITICAL:
                builder.setUserAuthenticationRequired(true)
                       .setUserAuthenticationValidityDurationSeconds(300) // 5 minutes
                       .setRandomizedEncryptionRequired(true);
                break;
            case AiSecurityManager.DATA_LEVEL_SENSITIVE:
                builder.setRandomizedEncryptionRequired(true);
                break;
            default:
                // Standard security for personal and public data
                break;
        }

        keyGenerator.init(builder.build());
        keyGenerator.generateKey();
    }

    private byte[] encryptBytes(byte[] data, int classificationLevel) throws Exception {
        String keyAlias = mKeyAliases.get(classificationLevel);
        if (keyAlias == null) {
            throw new IllegalArgumentException("Unknown classification level: " + classificationLevel);
        }

        SecretKey secretKey = (SecretKey) mKeyStore.getKey(keyAlias, null);
        if (secretKey == null) {
            throw new IllegalStateException("Encryption key not found for level: " + classificationLevel);
        }

        Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);

        byte[] iv = cipher.getIV();
        byte[] encryptedData = cipher.doFinal(data);

        // Combine IV and encrypted data
        byte[] result = new byte[iv.length + encryptedData.length];
        System.arraycopy(iv, 0, result, 0, iv.length);
        System.arraycopy(encryptedData, 0, result, iv.length, encryptedData.length);

        return result;
    }

    private byte[] decryptBytes(byte[] encryptedData, int classificationLevel) throws Exception {
        String keyAlias = mKeyAliases.get(classificationLevel);
        if (keyAlias == null) {
            throw new IllegalArgumentException("Unknown classification level: " + classificationLevel);
        }

        SecretKey secretKey = (SecretKey) mKeyStore.getKey(keyAlias, null);
        if (secretKey == null) {
            throw new IllegalStateException("Decryption key not found for level: " + classificationLevel);
        }

        // Extract IV and encrypted data
        byte[] iv = new byte[GCM_IV_LENGTH];
        byte[] cipherText = new byte[encryptedData.length - GCM_IV_LENGTH];
        System.arraycopy(encryptedData, 0, iv, 0, GCM_IV_LENGTH);
        System.arraycopy(encryptedData, GCM_IV_LENGTH, cipherText, 0, cipherText.length);

        Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
        GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec);

        return cipher.doFinal(cipherText);
    }

    private Map<String, Object> bundleToMap(Bundle bundle) {
        Map<String, Object> map = new HashMap<>();
        for (String key : bundle.keySet()) {
            Object value = bundle.get(key);
            if (value instanceof Bundle) {
                map.put(key, bundleToMap((Bundle) value));
            } else {
                map.put(key, value);
            }
        }
        return map;
    }

    private Bundle mapToBundle(Map<String, Object> map) {
        Bundle bundle = new Bundle();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> nestedMap = (Map<String, Object>) value;
                bundle.putBundle(key, mapToBundle(nestedMap));
            } else if (value instanceof String) {
                bundle.putString(key, (String) value);
            } else if (value instanceof Integer) {
                bundle.putInt(key, (Integer) value);
            } else if (value instanceof Long) {
                bundle.putLong(key, (Long) value);
            } else if (value instanceof Boolean) {
                bundle.putBoolean(key, (Boolean) value);
            } else if (value instanceof Double) {
                bundle.putDouble(key, (Double) value);
            } else if (value instanceof Float) {
                bundle.putFloat(key, (Float) value);
            } else {
                // Convert other types to string
                bundle.putString(key, String.valueOf(value));
            }
        }
        return bundle;
    }
}
