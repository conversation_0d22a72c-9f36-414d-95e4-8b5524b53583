/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef JARVIS_AI_CONTEXT_H
#define JARVIS_AI_CONTEXT_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Jarvis OS AI Context Library
 * 
 * Provides native context processing, analysis, and fusion capabilities
 * for the AI context engine.
 */

// Version information
#define AI_CONTEXT_VERSION_MAJOR 1
#define AI_CONTEXT_VERSION_MINOR 0
#define AI_CONTEXT_VERSION_PATCH 0

// Return codes
typedef enum {
    AI_CONTEXT_SUCCESS = 0,
    AI_CONTEXT_ERROR_INVALID_PARAM = -1,
    AI_CONTEXT_ERROR_OUT_OF_MEMORY = -2,
    AI_CONTEXT_ERROR_PROCESSING_FAILED = -3,
    AI_CONTEXT_ERROR_INVALID_FORMAT = -4,
    AI_CONTEXT_ERROR_INSUFFICIENT_DATA = -5
} ai_context_result_t;

// Context types
typedef enum {
    AI_CONTEXT_TYPE_APP_STATE = 0,
    AI_CONTEXT_TYPE_NOTIFICATION = 1,
    AI_CONTEXT_TYPE_SENSOR = 2,
    AI_CONTEXT_TYPE_LOCATION = 3,
    AI_CONTEXT_TYPE_COMMUNICATION = 4,
    AI_CONTEXT_TYPE_CALENDAR = 5,
    AI_CONTEXT_TYPE_USER_INTERACTION = 6,
    AI_CONTEXT_TYPE_ENVIRONMENTAL = 7
} ai_context_type_t;

// Context data formats
typedef enum {
    AI_CONTEXT_FORMAT_JSON = 0,
    AI_CONTEXT_FORMAT_PROTOBUF = 1,
    AI_CONTEXT_FORMAT_BINARY = 2,
    AI_CONTEXT_FORMAT_XML = 3
} ai_context_format_t;

// Forward declarations
typedef struct ai_context_processor ai_context_processor_t;
typedef struct ai_context_data ai_context_data_t;
typedef struct ai_context_fusion ai_context_fusion_t;

// Context data structure
struct ai_context_data {
    ai_context_type_t type;           // Type of context data
    ai_context_format_t format;       // Data format
    uint64_t timestamp;               // Timestamp when data was collected
    float confidence;                 // Confidence score (0.0 - 1.0)
    size_t data_size;                 // Size of data in bytes
    void* data;                       // Raw context data
    char source[64];                  // Source identifier
    uint32_t privacy_level;           // Privacy level (1-4)
};

// Context fusion configuration
typedef struct {
    float confidence_threshold;       // Minimum confidence for fusion
    size_t max_context_age_ms;       // Maximum age of context data
    bool enable_temporal_fusion;     // Enable temporal context fusion
    bool enable_spatial_fusion;      // Enable spatial context fusion
    float temporal_weight;           // Weight for temporal correlation
    float spatial_weight;            // Weight for spatial correlation
} ai_context_fusion_config_t;

// Context analysis results
typedef struct {
    float overall_confidence;        // Overall confidence of fused context
    size_t num_sources;             // Number of context sources used
    uint64_t fusion_timestamp;      // When fusion was performed
    ai_context_data_t* fused_data;  // Fused context data
    float* source_weights;          // Weight of each source in fusion
    char analysis_summary[256];     // Human-readable analysis summary
} ai_context_analysis_t;

// Pattern detection results
typedef struct {
    char pattern_name[64];          // Name of detected pattern
    float confidence;               // Confidence in pattern detection
    uint64_t first_occurrence;     // First time pattern was seen
    uint64_t last_occurrence;      // Last time pattern was seen
    uint32_t frequency;            // How often pattern occurs
    ai_context_type_t primary_type; // Primary context type for pattern
} ai_context_pattern_t;

/**
 * Initialize the AI context library
 * 
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_init(void);

/**
 * Cleanup and shutdown the AI context library
 */
void ai_context_cleanup(void);

/**
 * Get library version information
 * 
 * @param major Pointer to store major version
 * @param minor Pointer to store minor version
 * @param patch Pointer to store patch version
 */
void ai_context_get_version(int* major, int* minor, int* patch);

/**
 * Create a context processor
 * 
 * @param processor Pointer to store the created processor
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_processor_create(ai_context_processor_t** processor);

/**
 * Destroy a context processor
 * 
 * @param processor The processor to destroy
 */
void ai_context_processor_destroy(ai_context_processor_t* processor);

/**
 * Process raw context data
 * 
 * @param processor The context processor
 * @param raw_data Raw input data
 * @param data_size Size of raw data
 * @param context_type Type of context being processed
 * @param processed_data Pointer to store processed context data
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_process(ai_context_processor_t* processor,
                                      const void* raw_data,
                                      size_t data_size,
                                      ai_context_type_t context_type,
                                      ai_context_data_t** processed_data);

/**
 * Create a context fusion engine
 * 
 * @param config Fusion configuration
 * @param fusion Pointer to store the created fusion engine
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_fusion_create(const ai_context_fusion_config_t* config,
                                            ai_context_fusion_t** fusion);

/**
 * Destroy a context fusion engine
 * 
 * @param fusion The fusion engine to destroy
 */
void ai_context_fusion_destroy(ai_context_fusion_t* fusion);

/**
 * Add context data to fusion engine
 * 
 * @param fusion The fusion engine
 * @param context_data Context data to add
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_fusion_add_data(ai_context_fusion_t* fusion,
                                              const ai_context_data_t* context_data);

/**
 * Perform context fusion and analysis
 * 
 * @param fusion The fusion engine
 * @param analysis Pointer to store analysis results
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_fusion_analyze(ai_context_fusion_t* fusion,
                                             ai_context_analysis_t** analysis);

/**
 * Detect patterns in context data
 * 
 * @param fusion The fusion engine
 * @param patterns Array to store detected patterns
 * @param max_patterns Maximum number of patterns to detect
 * @param num_patterns Pointer to store actual number of patterns detected
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_detect_patterns(ai_context_fusion_t* fusion,
                                              ai_context_pattern_t* patterns,
                                              size_t max_patterns,
                                              size_t* num_patterns);

/**
 * Create context data structure
 * 
 * @param type Context type
 * @param format Data format
 * @param data Raw data
 * @param data_size Size of data
 * @param source Source identifier
 * @param context_data Pointer to store created context data
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_data_create(ai_context_type_t type,
                                          ai_context_format_t format,
                                          const void* data,
                                          size_t data_size,
                                          const char* source,
                                          ai_context_data_t** context_data);

/**
 * Destroy context data and free resources
 * 
 * @param context_data The context data to destroy
 */
void ai_context_data_destroy(ai_context_data_t* context_data);

/**
 * Serialize context data to buffer
 * 
 * @param context_data Context data to serialize
 * @param format Target format for serialization
 * @param buffer Output buffer
 * @param buffer_size Size of output buffer
 * @param actual_size Pointer to store actual serialized size
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_data_serialize(const ai_context_data_t* context_data,
                                             ai_context_format_t format,
                                             void* buffer,
                                             size_t buffer_size,
                                             size_t* actual_size);

/**
 * Deserialize context data from buffer
 * 
 * @param buffer Input buffer containing serialized data
 * @param buffer_size Size of input buffer
 * @param format Format of serialized data
 * @param context_data Pointer to store deserialized context data
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_data_deserialize(const void* buffer,
                                               size_t buffer_size,
                                               ai_context_format_t format,
                                               ai_context_data_t** context_data);

/**
 * Calculate similarity between two context data items
 * 
 * @param data1 First context data
 * @param data2 Second context data
 * @param similarity Pointer to store similarity score (0.0 - 1.0)
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_calculate_similarity(const ai_context_data_t* data1,
                                                   const ai_context_data_t* data2,
                                                   float* similarity);

/**
 * Extract features from context data for ML processing
 * 
 * @param context_data Input context data
 * @param features Output buffer for feature vector
 * @param max_features Maximum number of features to extract
 * @param num_features Pointer to store actual number of features
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_extract_features(const ai_context_data_t* context_data,
                                               float* features,
                                               size_t max_features,
                                               size_t* num_features);

/**
 * Validate context data integrity and format
 * 
 * @param context_data Context data to validate
 * @param is_valid Pointer to store validation result
 * @return AI_CONTEXT_SUCCESS on success, error code otherwise
 */
ai_context_result_t ai_context_validate(const ai_context_data_t* context_data,
                                       bool* is_valid);

/**
 * Get error message for a result code
 * 
 * @param result The result code
 * @return Human-readable error message
 */
const char* ai_context_get_error_message(ai_context_result_t result);

/**
 * Enable or disable debug logging
 * 
 * @param enable true to enable debug logging, false to disable
 */
void ai_context_set_debug_logging(bool enable);

#ifdef __cplusplus
}
#endif

#endif // JARVIS_AI_CONTEXT_H
